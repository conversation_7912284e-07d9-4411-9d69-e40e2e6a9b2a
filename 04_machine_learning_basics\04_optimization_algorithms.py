"""
优化算法 - 梯度下降及其变种
理解不同优化算法的原理和特点，为深度学习优化打下基础
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict, Callable
import time

class OptimizationAlgorithms:
    """优化算法集合"""
    
    def __init__(self, learning_rate: float = 0.01):
        self.learning_rate = learning_rate
        self.reset_state()
    
    def reset_state(self):
        """重置优化器状态"""
        self.momentum_v = {}  # Momentum的速度
        self.adam_m = {}      # Adam的一阶矩估计
        self.adam_v = {}      # Adam的二阶矩估计
        self.rmsprop_v = {}   # RMSprop的累积平方梯度
        self.iteration = 0    # 迭代次数
    
    def sgd(self, params: Dict[str, np.ndarray], gradients: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """随机梯度下降 (SGD)"""
        updated_params = {}
        
        for name, param in params.items():
            updated_params[name] = param - self.learning_rate * gradients[name]
        
        return updated_params
    
    def momentum(self, params: Dict[str, np.ndarray], gradients: Dict[str, np.ndarray], 
                beta: float = 0.9) -> Dict[str, np.ndarray]:
        """Momentum优化器"""
        updated_params = {}
        
        for name, param in params.items():
            # 初始化速度
            if name not in self.momentum_v:
                self.momentum_v[name] = np.zeros_like(param)
            
            # 更新速度：v = β * v + (1-β) * gradient
            self.momentum_v[name] = beta * self.momentum_v[name] + (1 - beta) * gradients[name]
            
            # 更新参数：param = param - lr * v
            updated_params[name] = param - self.learning_rate * self.momentum_v[name]
        
        return updated_params
    
    def rmsprop(self, params: Dict[str, np.ndarray], gradients: Dict[str, np.ndarray], 
               beta: float = 0.9, epsilon: float = 1e-8) -> Dict[str, np.ndarray]:
        """RMSprop优化器"""
        updated_params = {}
        
        for name, param in params.items():
            # 初始化累积平方梯度
            if name not in self.rmsprop_v:
                self.rmsprop_v[name] = np.zeros_like(param)
            
            # 更新累积平方梯度：v = β * v + (1-β) * gradient²
            self.rmsprop_v[name] = beta * self.rmsprop_v[name] + (1 - beta) * gradients[name] ** 2
            
            # 更新参数：param = param - lr * gradient / (√v + ε)
            updated_params[name] = param - self.learning_rate * gradients[name] / (np.sqrt(self.rmsprop_v[name]) + epsilon)
        
        return updated_params
    
    def adam(self, params: Dict[str, np.ndarray], gradients: Dict[str, np.ndarray], 
            beta1: float = 0.9, beta2: float = 0.999, epsilon: float = 1e-8) -> Dict[str, np.ndarray]:
        """Adam优化器"""
        self.iteration += 1
        updated_params = {}
        
        for name, param in params.items():
            # 初始化矩估计
            if name not in self.adam_m:
                self.adam_m[name] = np.zeros_like(param)
                self.adam_v[name] = np.zeros_like(param)
            
            # 更新一阶矩估计：m = β₁ * m + (1-β₁) * gradient
            self.adam_m[name] = beta1 * self.adam_m[name] + (1 - beta1) * gradients[name]
            
            # 更新二阶矩估计：v = β₂ * v + (1-β₂) * gradient²
            self.adam_v[name] = beta2 * self.adam_v[name] + (1 - beta2) * gradients[name] ** 2
            
            # 偏差修正
            m_corrected = self.adam_m[name] / (1 - beta1 ** self.iteration)
            v_corrected = self.adam_v[name] / (1 - beta2 ** self.iteration)
            
            # 更新参数：param = param - lr * m_corrected / (√v_corrected + ε)
            updated_params[name] = param - self.learning_rate * m_corrected / (np.sqrt(v_corrected) + epsilon)
        
        return updated_params

class OptimizationComparison:
    """优化算法比较类"""
    
    def __init__(self, objective_function: Callable, gradient_function: Callable):
        self.objective_function = objective_function
        self.gradient_function = gradient_function
    
    def optimize(self, optimizer_name: str, initial_params: Dict[str, np.ndarray], 
                num_iterations: int = 1000, **optimizer_kwargs) -> Tuple[List[float], List[Dict[str, np.ndarray]]]:
        """使用指定优化器进行优化"""
        
        # 创建优化器
        optimizer = OptimizationAlgorithms(**optimizer_kwargs)
        
        # 记录历史
        loss_history = []
        param_history = []
        
        current_params = {name: param.copy() for name, param in initial_params.items()}
        
        for iteration in range(num_iterations):
            # 计算损失
            loss = self.objective_function(current_params)
            loss_history.append(loss)
            param_history.append({name: param.copy() for name, param in current_params.items()})
            
            # 计算梯度
            gradients = self.gradient_function(current_params)
            
            # 更新参数
            if optimizer_name == 'sgd':
                current_params = optimizer.sgd(current_params, gradients)
            elif optimizer_name == 'momentum':
                current_params = optimizer.momentum(current_params, gradients)
            elif optimizer_name == 'rmsprop':
                current_params = optimizer.rmsprop(current_params, gradients)
            elif optimizer_name == 'adam':
                current_params = optimizer.adam(current_params, gradients)
            else:
                raise ValueError(f"未知的优化器: {optimizer_name}")
        
        return loss_history, param_history

# 测试函数定义
def quadratic_function(params: Dict[str, np.ndarray]) -> float:
    """二次函数：f(x, y) = x² + y²"""
    x, y = params['x'][0], params['y'][0]
    return x**2 + y**2

def quadratic_gradient(params: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """二次函数的梯度"""
    x, y = params['x'][0], params['y'][0]
    return {
        'x': np.array([2 * x]),
        'y': np.array([2 * y])
    }

def rosenbrock_function(params: Dict[str, np.ndarray]) -> float:
    """Rosenbrock函数：f(x, y) = (1-x)² + 100(y-x²)²"""
    x, y = params['x'][0], params['y'][0]
    return (1 - x)**2 + 100 * (y - x**2)**2

def rosenbrock_gradient(params: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """Rosenbrock函数的梯度"""
    x, y = params['x'][0], params['y'][0]
    dx = -2 * (1 - x) - 400 * x * (y - x**2)
    dy = 200 * (y - x**2)
    return {
        'x': np.array([dx]),
        'y': np.array([dy])
    }

def beale_function(params: Dict[str, np.ndarray]) -> float:
    """Beale函数：复杂的优化测试函数"""
    x, y = params['x'][0], params['y'][0]
    term1 = (1.5 - x + x*y)**2
    term2 = (2.25 - x + x*y**2)**2
    term3 = (2.625 - x + x*y**3)**2
    return term1 + term2 + term3

def beale_gradient(params: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """Beale函数的梯度"""
    x, y = params['x'][0], params['y'][0]
    
    # 计算各项的导数
    term1 = 1.5 - x + x*y
    term2 = 2.25 - x + x*y**2
    term3 = 2.625 - x + x*y**3
    
    dx = 2*term1*(-1 + y) + 2*term2*(-1 + y**2) + 2*term3*(-1 + y**3)
    dy = 2*term1*x + 2*term2*x*2*y + 2*term3*x*3*y**2
    
    return {
        'x': np.array([dx]),
        'y': np.array([dy])
    }

def demonstrate_basic_optimizers():
    """演示基本优化器"""
    print("=== 基本优化器演示 ===")
    
    # 使用简单的二次函数
    initial_params = {'x': np.array([5.0]), 'y': np.array([5.0])}
    
    optimizers = ['sgd', 'momentum', 'rmsprop', 'adam']
    learning_rates = {'sgd': 0.1, 'momentum': 0.1, 'rmsprop': 0.1, 'adam': 0.1}
    
    comparison = OptimizationComparison(quadratic_function, quadratic_gradient)
    
    results = {}
    
    for optimizer_name in optimizers:
        print(f"\n{optimizer_name.upper()} 优化器:")
        
        loss_history, param_history = comparison.optimize(
            optimizer_name, 
            initial_params, 
            num_iterations=100,
            learning_rate=learning_rates[optimizer_name]
        )
        
        results[optimizer_name] = {
            'loss_history': loss_history,
            'param_history': param_history
        }
        
        final_loss = loss_history[-1]
        final_params = param_history[-1]
        
        print(f"  最终损失: {final_loss:.8f}")
        print(f"  最终参数: x={final_params['x'][0]:.6f}, y={final_params['y'][0]:.6f}")
        print(f"  收敛速度: {len([l for l in loss_history if l > 0.01])} 步达到0.01")
    
    return results

def demonstrate_learning_rate_effects():
    """演示学习率的影响"""
    print("\n=== 学习率影响演示 ===")
    
    initial_params = {'x': np.array([5.0]), 'y': np.array([5.0])}
    learning_rates = [0.01, 0.1, 0.5, 1.0]
    
    comparison = OptimizationComparison(quadratic_function, quadratic_gradient)
    
    for lr in learning_rates:
        print(f"\n学习率: {lr}")
        
        loss_history, param_history = comparison.optimize(
            'sgd', 
            initial_params, 
            num_iterations=50,
            learning_rate=lr
        )
        
        final_loss = loss_history[-1]
        
        print(f"  最终损失: {final_loss:.8f}")
        
        # 检查收敛性
        if len(loss_history) > 10:
            recent_losses = loss_history[-10:]
            if max(recent_losses) - min(recent_losses) < 1e-8:
                print("  ✓ 已收敛")
            elif any(np.isnan(recent_losses)) or any(np.isinf(recent_losses)):
                print("  ✗ 发散（数值不稳定）")
            else:
                print("  ⚠ 仍在收敛中")
        
        if lr >= 1.0:
            print("  ⚠️ 学习率过大可能导致震荡或发散")

def demonstrate_complex_optimization():
    """演示复杂函数优化"""
    print("\n=== 复杂函数优化演示 ===")
    
    # 使用Rosenbrock函数
    initial_params = {'x': np.array([-1.0]), 'y': np.array([1.0])}
    
    comparison = OptimizationComparison(rosenbrock_function, rosenbrock_gradient)
    
    optimizers = ['sgd', 'momentum', 'rmsprop', 'adam']
    learning_rates = {'sgd': 0.001, 'momentum': 0.001, 'rmsprop': 0.01, 'adam': 0.01}
    
    print("Rosenbrock函数优化 (全局最优解: x=1, y=1)")
    
    for optimizer_name in optimizers:
        print(f"\n{optimizer_name.upper()}:")
        
        loss_history, param_history = comparison.optimize(
            optimizer_name,
            initial_params,
            num_iterations=1000,
            learning_rate=learning_rates[optimizer_name]
        )
        
        final_loss = loss_history[-1]
        final_params = param_history[-1]
        
        print(f"  最终损失: {final_loss:.8f}")
        print(f"  最终参数: x={final_params['x'][0]:.6f}, y={final_params['y'][0]:.6f}")
        
        # 计算与最优解的距离
        optimal_x, optimal_y = 1.0, 1.0
        distance = np.sqrt((final_params['x'][0] - optimal_x)**2 + (final_params['y'][0] - optimal_y)**2)
        print(f"  与最优解距离: {distance:.6f}")

def demonstrate_momentum_effect():
    """演示动量的效果"""
    print("\n=== 动量效果演示 ===")
    
    initial_params = {'x': np.array([5.0]), 'y': np.array([5.0])}
    
    # 比较不同动量系数
    momentum_values = [0.0, 0.5, 0.9, 0.99]
    
    comparison = OptimizationComparison(quadratic_function, quadratic_gradient)
    
    for momentum_beta in momentum_values:
        print(f"\n动量系数: {momentum_beta}")
        
        optimizer = OptimizationAlgorithms(learning_rate=0.1)
        
        loss_history = []
        current_params = {name: param.copy() for name, param in initial_params.items()}
        
        for iteration in range(50):
            loss = quadratic_function(current_params)
            loss_history.append(loss)
            
            gradients = quadratic_gradient(current_params)
            current_params = optimizer.momentum(current_params, gradients, beta=momentum_beta)
        
        final_loss = loss_history[-1]
        print(f"  最终损失: {final_loss:.8f}")
        
        # 计算收敛速度
        convergence_step = None
        for i, loss in enumerate(loss_history):
            if loss < 0.01:
                convergence_step = i
                break
        
        if convergence_step is not None:
            print(f"  收敛步数 (损失<0.01): {convergence_step}")
        else:
            print(f"  未在50步内收敛到0.01")

def demonstrate_adaptive_learning_rates():
    """演示自适应学习率"""
    print("\n=== 自适应学习率演示 ===")
    
    # 创建一个各维度梯度差异很大的函数
    def anisotropic_function(params):
        x, y = params['x'][0], params['y'][0]
        return 100 * x**2 + y**2  # x方向梯度比y方向大100倍
    
    def anisotropic_gradient(params):
        x, y = params['x'][0], params['y'][0]
        return {
            'x': np.array([200 * x]),
            'y': np.array([2 * y])
        }
    
    initial_params = {'x': np.array([1.0]), 'y': np.array([1.0])}
    
    comparison = OptimizationComparison(anisotropic_function, anisotropic_gradient)
    
    optimizers = ['sgd', 'rmsprop', 'adam']
    learning_rates = {'sgd': 0.001, 'rmsprop': 0.01, 'adam': 0.01}
    
    print("各向异性函数优化 (x方向梯度比y方向大100倍)")
    
    for optimizer_name in optimizers:
        print(f"\n{optimizer_name.upper()}:")
        
        loss_history, param_history = comparison.optimize(
            optimizer_name,
            initial_params,
            num_iterations=200,
            learning_rate=learning_rates[optimizer_name]
        )
        
        final_loss = loss_history[-1]
        final_params = param_history[-1]
        
        print(f"  最终损失: {final_loss:.8f}")
        print(f"  最终参数: x={final_params['x'][0]:.6f}, y={final_params['y'][0]:.6f}")
        
        if optimizer_name == 'sgd':
            print("  ⚠️ SGD在各向异性函数上收敛较慢")
        else:
            print("  ✓ 自适应学习率有助于处理各向异性")

def performance_comparison():
    """性能比较"""
    print("\n=== 性能比较 ===")
    
    initial_params = {'x': np.array([5.0]), 'y': np.array([5.0])}
    num_iterations = 1000
    
    comparison = OptimizationComparison(quadratic_function, quadratic_gradient)
    
    optimizers = ['sgd', 'momentum', 'rmsprop', 'adam']
    
    for optimizer_name in optimizers:
        start_time = time.time()
        
        loss_history, _ = comparison.optimize(
            optimizer_name,
            initial_params,
            num_iterations=num_iterations,
            learning_rate=0.1
        )
        
        end_time = time.time()
        
        print(f"{optimizer_name.upper()}:")
        print(f"  执行时间: {end_time - start_time:.4f}秒")
        print(f"  最终损失: {loss_history[-1]:.8f}")

# 主程序演示
if __name__ == "__main__":
    print("优化算法学习")
    print("=" * 50)
    
    demonstrate_basic_optimizers()
    demonstrate_learning_rate_effects()
    demonstrate_complex_optimization()
    demonstrate_momentum_effect()
    demonstrate_adaptive_learning_rates()
    performance_comparison()
    
    print("\n" + "=" * 50)
    print("优化算法总结:")
    print("✓ SGD：基础梯度下降，简单但可能收敛慢")
    print("✓ Momentum：加速收敛，减少震荡")
    print("✓ RMSprop：自适应学习率，适合各向异性函数")
    print("✓ Adam：结合Momentum和RMSprop的优点")
    print("✓ 学习率：关键超参数，影响收敛速度和稳定性")
    print("✓ 自适应方法：在复杂优化问题上表现更好")
    print("\n这些优化算法是训练深度神经网络的重要工具！")
