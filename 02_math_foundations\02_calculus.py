"""
微积分基础 - 优化和梯度下降的数学基础
重点理解导数、梯度，这些是反向传播算法的核心
"""

import math
from typing import Callable, List, Tuple

# 1. 数值导数计算
def numerical_derivative(f: Callable[[float], float], x: float, h: float = 1e-7) -> float:
    """数值方法计算导数 - 理解导数的定义"""
    return (f(x + h) - f(x - h)) / (2 * h)

def numerical_partial_derivative(f: Callable[[List[float]], float], 
                               x: List[float], var_index: int, h: float = 1e-7) -> float:
    """数值方法计算偏导数"""
    x_plus = x.copy()
    x_minus = x.copy()
    x_plus[var_index] += h
    x_minus[var_index] -= h
    return (f(x_plus) - f(x_minus)) / (2 * h)

def numerical_gradient(f: Callable[[List[float]], float], x: List[float], h: float = 1e-7) -> List[float]:
    """数值方法计算梯度 - 所有偏导数的向量"""
    gradient = []
    for i in range(len(x)):
        partial_deriv = numerical_partial_derivative(f, x, i, h)
        gradient.append(partial_deriv)
    return gradient

# 2. 常见函数的解析导数
class AnalyticalDerivatives:
    """常见函数的解析导数 - 理解导数的计算规则"""
    
    @staticmethod
    def power_rule(x: float, n: float) -> float:
        """幂函数导数: d/dx(x^n) = n*x^(n-1)"""
        if n == 0:
            return 0
        return n * (x ** (n - 1))
    
    @staticmethod
    def exponential(x: float) -> float:
        """指数函数导数: d/dx(e^x) = e^x"""
        return math.exp(x)
    
    @staticmethod
    def logarithm(x: float) -> float:
        """对数函数导数: d/dx(ln(x)) = 1/x"""
        if x <= 0:
            raise ValueError("对数函数的定义域为正数")
        return 1 / x
    
    @staticmethod
    def sigmoid_derivative(x: float) -> float:
        """Sigmoid函数导数: σ'(x) = σ(x)(1-σ(x))"""
        sigmoid_x = 1 / (1 + math.exp(-x))
        return sigmoid_x * (1 - sigmoid_x)
    
    @staticmethod
    def relu_derivative(x: float) -> float:
        """ReLU函数导数"""
        return 1.0 if x > 0 else 0.0
    
    @staticmethod
    def tanh_derivative(x: float) -> float:
        """Tanh函数导数: tanh'(x) = 1 - tanh²(x)"""
        tanh_x = math.tanh(x)
        return 1 - tanh_x ** 2

# 3. 链式法则实现
class ChainRule:
    """链式法则 - 复合函数求导，反向传播的基础"""
    
    @staticmethod
    def compose_two_functions(f_derivative: Callable[[float], float],
                            g_derivative: Callable[[float], float],
                            g: Callable[[float], float],
                            x: float) -> float:
        """两个函数复合的导数: (f∘g)'(x) = f'(g(x)) * g'(x)"""
        return f_derivative(g(x)) * g_derivative(x)
    
    @staticmethod
    def demonstrate_chain_rule():
        """演示链式法则"""
        # 例子: f(x) = (2x + 1)³
        # 设 g(x) = 2x + 1, f(u) = u³
        # 则 f'(u) = 3u², g'(x) = 2
        # (f∘g)'(x) = 3(2x+1)² * 2 = 6(2x+1)²
        
        def g(x):
            return 2 * x + 1
        
        def f_derivative(u):
            return 3 * u ** 2
        
        def g_derivative(x):
            return 2
        
        x = 2.0
        analytical_result = 6 * (2 * x + 1) ** 2
        chain_rule_result = ChainRule.compose_two_functions(f_derivative, g_derivative, g, x)
        
        return analytical_result, chain_rule_result

# 4. 梯度下降算法
class GradientDescent:
    """梯度下降算法 - 机器学习优化的核心"""
    
    def __init__(self, learning_rate: float = 0.01, max_iterations: int = 1000, tolerance: float = 1e-6):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.history = []
    
    def optimize(self, f: Callable[[List[float]], float], 
                initial_point: List[float]) -> Tuple[List[float], List[float]]:
        """梯度下降优化"""
        x = initial_point.copy()
        self.history = [x.copy()]
        
        for iteration in range(self.max_iterations):
            # 计算梯度
            grad = numerical_gradient(f, x)
            
            # 检查收敛条件
            grad_norm = math.sqrt(sum(g**2 for g in grad))
            if grad_norm < self.tolerance:
                print(f"在第 {iteration} 次迭代后收敛")
                break
            
            # 更新参数: x = x - α∇f(x)
            x = [x[i] - self.learning_rate * grad[i] for i in range(len(x))]
            self.history.append(x.copy())
        
        return x, self.history

# 5. 多元函数优化示例
def quadratic_function(x: List[float]) -> float:
    """二次函数: f(x,y) = x² + y² - 2x - 4y + 5"""
    return x[0]**2 + x[1]**2 - 2*x[0] - 4*x[1] + 5

def rosenbrock_function(x: List[float]) -> float:
    """Rosenbrock函数: f(x,y) = (1-x)² + 100(y-x²)²"""
    return (1 - x[0])**2 + 100 * (x[1] - x[0]**2)**2

def neural_network_loss(weights: List[float]) -> float:
    """简化的神经网络损失函数"""
    # 模拟一个简单的损失函数
    w1, w2, b = weights
    # 假设有一些训练数据点
    loss = 0
    data_points = [(1, 2, 3), (2, 3, 5), (3, 4, 7)]  # (x1, x2, target)
    
    for x1, x2, target in data_points:
        prediction = w1 * x1 + w2 * x2 + b
        loss += (prediction - target) ** 2
    
    return loss / len(data_points)

# 6. 反向传播的数学基础
class BackpropagationMath:
    """反向传播的数学原理"""
    
    @staticmethod
    def simple_network_gradients(x: float, y_true: float, w1: float, w2: float, b: float):
        """简单网络的梯度计算
        网络结构: x -> w1 -> ReLU -> w2 -> output
        损失函数: MSE
        """
        # 前向传播
        z1 = w1 * x + b
        a1 = max(0, z1)  # ReLU
        output = w2 * a1
        
        # 损失
        loss = 0.5 * (output - y_true) ** 2
        
        # 反向传播
        # ∂L/∂output
        dL_doutput = output - y_true
        
        # ∂L/∂w2 = ∂L/∂output * ∂output/∂w2
        dL_dw2 = dL_doutput * a1
        
        # ∂L/∂a1 = ∂L/∂output * ∂output/∂a1
        dL_da1 = dL_doutput * w2
        
        # ∂L/∂z1 = ∂L/∂a1 * ∂a1/∂z1 (ReLU导数)
        dL_dz1 = dL_da1 * (1 if z1 > 0 else 0)
        
        # ∂L/∂w1 = ∂L/∂z1 * ∂z1/∂w1
        dL_dw1 = dL_dz1 * x
        
        # ∂L/∂b = ∂L/∂z1 * ∂z1/∂b
        dL_db = dL_dz1 * 1
        
        return {
            'loss': loss,
            'dL_dw1': dL_dw1,
            'dL_dw2': dL_dw2,
            'dL_db': dL_db,
            'forward_values': {
                'z1': z1,
                'a1': a1,
                'output': output
            }
        }

# 演示使用
if __name__ == "__main__":
    print("=== 微积分基础演示 ===")
    
    # 1. 数值导数
    print("1. 数值导数计算:")
    
    def f(x):
        return x**2 + 3*x + 2
    
    x = 2.0
    numerical_deriv = numerical_derivative(f, x)
    analytical_deriv = 2*x + 3  # 解析解
    
    print(f"函数: f(x) = x² + 3x + 2")
    print(f"在 x = {x} 处:")
    print(f"数值导数: {numerical_deriv:.6f}")
    print(f"解析导数: {analytical_deriv:.6f}")
    print(f"误差: {abs(numerical_deriv - analytical_deriv):.8f}")
    
    # 2. 多元函数梯度
    print("\n2. 多元函数梯度:")
    
    def g(x):
        return x[0]**2 + x[1]**2
    
    point = [3.0, 4.0]
    grad = numerical_gradient(g, point)
    analytical_grad = [2*point[0], 2*point[1]]
    
    print(f"函数: g(x,y) = x² + y²")
    print(f"在点 {point} 处:")
    print(f"数值梯度: {[f'{g:.6f}' for g in grad]}")
    print(f"解析梯度: {analytical_grad}")
    
    # 3. 激活函数导数
    print("\n3. 激活函数导数:")
    x = 0.5
    print(f"在 x = {x} 处:")
    print(f"Sigmoid导数: {AnalyticalDerivatives.sigmoid_derivative(x):.6f}")
    print(f"ReLU导数: {AnalyticalDerivatives.relu_derivative(x):.6f}")
    print(f"Tanh导数: {AnalyticalDerivatives.tanh_derivative(x):.6f}")
    
    # 4. 链式法则演示
    print("\n4. 链式法则:")
    analytical, chain_rule = ChainRule.demonstrate_chain_rule()
    print(f"函数: f(x) = (2x + 1)³ 在 x = 2")
    print(f"解析结果: {analytical}")
    print(f"链式法则: {chain_rule}")
    
    # 5. 梯度下降优化
    print("\n5. 梯度下降优化:")
    
    # 优化二次函数
    optimizer = GradientDescent(learning_rate=0.1, max_iterations=100)
    initial_point = [0.0, 0.0]
    
    print("优化函数: f(x,y) = x² + y² - 2x - 4y + 5")
    print(f"初始点: {initial_point}")
    
    optimal_point, history = optimizer.optimize(quadratic_function, initial_point)
    
    print(f"最优点: {[f'{x:.6f}' for x in optimal_point]}")
    print(f"最优值: {quadratic_function(optimal_point):.6f}")
    print(f"理论最优点: [1, 2] (解析解)")
    print(f"迭代次数: {len(history)}")
    
    # 6. 反向传播数学
    print("\n6. 反向传播数学:")
    
    # 简单网络的梯度计算
    x, y_true = 2.0, 5.0
    w1, w2, b = 1.0, 1.5, 0.5
    
    result = BackpropagationMath.simple_network_gradients(x, y_true, w1, w2, b)
    
    print(f"输入: x = {x}, 目标: y = {y_true}")
    print(f"权重: w1 = {w1}, w2 = {w2}, b = {b}")
    print(f"损失: {result['loss']:.6f}")
    print(f"梯度:")
    print(f"  ∂L/∂w1 = {result['dL_dw1']:.6f}")
    print(f"  ∂L/∂w2 = {result['dL_dw2']:.6f}")
    print(f"  ∂L/∂b = {result['dL_db']:.6f}")
    
    print("\n=== 关键概念总结 ===")
    print("✓ 导数：函数变化率，优化的基础")
    print("✓ 偏导数：多元函数中单个变量的变化率")
    print("✓ 梯度：所有偏导数组成的向量，指向最大增长方向")
    print("✓ 链式法则：复合函数求导，反向传播的数学基础")
    print("✓ 梯度下降：沿负梯度方向优化参数")
    print("✓ 反向传播：利用链式法则高效计算神经网络梯度")
