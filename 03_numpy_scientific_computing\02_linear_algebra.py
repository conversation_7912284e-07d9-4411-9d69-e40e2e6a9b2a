"""
NumPy线性代数 - 矩阵运算和线性代数操作
这些操作是神经网络计算的基础，理解它们对学习深度学习至关重要
"""

import numpy as np
import time

def demonstrate_matrix_operations():
    """演示基础矩阵运算"""
    print("=== 基础矩阵运算 ===")
    
    # 创建矩阵
    A = np.array([[1, 2, 3],
                  [4, 5, 6]])
    B = np.array([[7, 8],
                  [9, 10],
                  [11, 12]])
    
    print(f"矩阵A (2x3):\n{A}")
    print(f"矩阵B (3x2):\n{B}")
    
    # 矩阵乘法
    C = A @ B  # 或者 np.dot(A, B)
    print(f"A @ B (矩阵乘法):\n{C}")
    
    # 转置
    print(f"A的转置:\n{A.T}")
    print(f"B的转置:\n{B.T}")
    
    # 方阵运算
    square_matrix = np.array([[1, 2, 3],
                             [4, 5, 6],
                             [7, 8, 10]])  # 注意：改为10使其可逆
    
    print(f"\n方阵:\n{square_matrix}")
    
    # 行列式
    det = np.linalg.det(square_matrix)
    print(f"行列式: {det:.4f}")
    
    # 逆矩阵（如果存在）
    if abs(det) > 1e-10:
        inv_matrix = np.linalg.inv(square_matrix)
        print(f"逆矩阵:\n{inv_matrix}")
        
        # 验证 A * A^(-1) = I
        identity_check = square_matrix @ inv_matrix
        print(f"验证 A @ A^(-1):\n{identity_check}")
    
    # 矩阵的迹（对角线元素之和）
    trace = np.trace(square_matrix)
    print(f"矩阵的迹: {trace}")

def demonstrate_vector_operations():
    """演示向量运算"""
    print("\n=== 向量运算 ===")
    
    # 创建向量
    v1 = np.array([1, 2, 3])
    v2 = np.array([4, 5, 6])
    
    print(f"向量v1: {v1}")
    print(f"向量v2: {v2}")
    
    # 点积（内积）
    dot_product = np.dot(v1, v2)
    print(f"点积 v1·v2: {dot_product}")
    
    # 向量长度（范数）
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    print(f"||v1||: {norm_v1:.4f}")
    print(f"||v2||: {norm_v2:.4f}")
    
    # 单位向量
    unit_v1 = v1 / norm_v1
    print(f"v1的单位向量: {unit_v1}")
    print(f"单位向量的长度: {np.linalg.norm(unit_v1):.6f}")
    
    # 向量夹角（余弦相似度）
    cos_angle = dot_product / (norm_v1 * norm_v2)
    angle_rad = np.arccos(cos_angle)
    angle_deg = np.degrees(angle_rad)
    print(f"向量夹角: {angle_deg:.2f}度")
    
    # 叉积（仅适用于3D向量）
    cross_product = np.cross(v1, v2)
    print(f"叉积 v1×v2: {cross_product}")
    
    # 外积
    outer_product = np.outer(v1, v2)
    print(f"外积 v1⊗v2:\n{outer_product}")

def demonstrate_eigenvalues_eigenvectors():
    """演示特征值和特征向量"""
    print("\n=== 特征值和特征向量 ===")
    
    # 创建对称矩阵（保证实特征值）
    A = np.array([[4, 2, 1],
                  [2, 3, 0],
                  [1, 0, 2]])
    
    print(f"矩阵A:\n{A}")
    
    # 计算特征值和特征向量
    eigenvalues, eigenvectors = np.linalg.eig(A)
    
    print(f"特征值: {eigenvalues}")
    print(f"特征向量:\n{eigenvectors}")
    
    # 验证特征值方程: A * v = λ * v
    for i in range(len(eigenvalues)):
        λ = eigenvalues[i]
        v = eigenvectors[:, i]
        
        Av = A @ v
        λv = λ * v
        
        print(f"\n特征值 {i+1}: λ = {λ:.4f}")
        print(f"A*v = {Av}")
        print(f"λ*v = {λv}")
        print(f"误差: {np.linalg.norm(Av - λv):.8f}")

def demonstrate_svd():
    """演示奇异值分解"""
    print("\n=== 奇异值分解 (SVD) ===")
    
    # 创建矩阵
    A = np.array([[1, 2, 3],
                  [4, 5, 6],
                  [7, 8, 9],
                  [10, 11, 12]])
    
    print(f"原矩阵A (4x3):\n{A}")
    
    # SVD分解: A = U * Σ * V^T
    U, s, Vt = np.linalg.svd(A, full_matrices=False)
    
    print(f"U矩阵形状: {U.shape}")
    print(f"奇异值: {s}")
    print(f"V^T矩阵形状: {Vt.shape}")
    
    # 重构矩阵
    S = np.diag(s)
    A_reconstructed = U @ S @ Vt
    
    print(f"重构矩阵:\n{A_reconstructed}")
    print(f"重构误差: {np.linalg.norm(A - A_reconstructed):.8f}")
    
    # 低秩近似
    k = 2  # 保留前k个奇异值
    A_approx = U[:, :k] @ np.diag(s[:k]) @ Vt[:k, :]
    
    print(f"\n低秩近似 (k={k}):\n{A_approx}")
    print(f"近似误差: {np.linalg.norm(A - A_approx):.4f}")

def demonstrate_solving_linear_systems():
    """演示线性方程组求解"""
    print("\n=== 线性方程组求解 ===")
    
    # 求解 Ax = b
    A = np.array([[2, 1, -1],
                  [-3, -1, 2],
                  [-2, 1, 2]])
    b = np.array([8, -11, -3])
    
    print(f"系数矩阵A:\n{A}")
    print(f"常数向量b: {b}")
    
    # 使用numpy求解
    x = np.linalg.solve(A, b)
    print(f"解向量x: {x}")
    
    # 验证解
    verification = A @ x
    print(f"验证 A@x: {verification}")
    print(f"目标 b: {b}")
    print(f"误差: {np.linalg.norm(verification - b):.8f}")
    
    # 最小二乘解（用于超定系统）
    A_overdetermined = np.array([[1, 1],
                                [1, 2],
                                [1, 3],
                                [1, 4]])
    b_overdetermined = np.array([6, 8, 10, 12])
    
    print(f"\n超定系统 (4x2):")
    print(f"A:\n{A_overdetermined}")
    print(f"b: {b_overdetermined}")
    
    x_lstsq, residuals, rank, s = np.linalg.lstsq(A_overdetermined, b_overdetermined, rcond=None)
    print(f"最小二乘解: {x_lstsq}")
    print(f"残差: {residuals}")

def neural_network_example():
    """神经网络中的线性代数应用"""
    print("\n=== 神经网络中的线性代数应用 ===")
    
    # 模拟一个简单的全连接层
    batch_size = 32
    input_dim = 784  # 28x28图像展平
    hidden_dim = 128
    output_dim = 10  # 10个类别
    
    # 输入数据 (batch_size, input_dim)
    X = np.random.randn(batch_size, input_dim)
    
    # 权重矩阵
    W1 = np.random.randn(input_dim, hidden_dim) * 0.01
    b1 = np.zeros(hidden_dim)
    
    W2 = np.random.randn(hidden_dim, output_dim) * 0.01
    b2 = np.zeros(output_dim)
    
    print(f"输入形状: {X.shape}")
    print(f"W1形状: {W1.shape}")
    print(f"W2形状: {W2.shape}")
    
    # 前向传播
    # 第一层: X @ W1 + b1
    Z1 = X @ W1 + b1  # 广播加法
    A1 = np.maximum(0, Z1)  # ReLU激活
    
    # 第二层: A1 @ W2 + b2
    Z2 = A1 @ W2 + b2
    
    print(f"隐藏层输出形状: {A1.shape}")
    print(f"最终输出形状: {Z2.shape}")
    
    # Softmax
    exp_scores = np.exp(Z2 - np.max(Z2, axis=1, keepdims=True))
    probs = exp_scores / np.sum(exp_scores, axis=1, keepdims=True)
    
    print(f"概率输出形状: {probs.shape}")
    print(f"第一个样本的概率分布: {probs[0]}")
    print(f"概率和: {np.sum(probs[0]):.6f}")

def performance_optimization():
    """性能优化技巧"""
    print("\n=== 性能优化技巧 ===")
    
    # 比较不同矩阵乘法方法的性能
    size = 1000
    A = np.random.randn(size, size)
    B = np.random.randn(size, size)
    
    # 方法1: @ 操作符
    start_time = time.time()
    C1 = A @ B
    time1 = time.time() - start_time
    
    # 方法2: np.dot
    start_time = time.time()
    C2 = np.dot(A, B)
    time2 = time.time() - start_time
    
    # 方法3: np.matmul
    start_time = time.time()
    C3 = np.matmul(A, B)
    time3 = time.time() - start_time
    
    print(f"矩阵大小: {size}x{size}")
    print(f"@ 操作符时间: {time1:.4f}秒")
    print(f"np.dot时间: {time2:.4f}秒")
    print(f"np.matmul时间: {time3:.4f}秒")
    
    # 验证结果一致性
    print(f"结果一致性: {np.allclose(C1, C2) and np.allclose(C2, C3)}")
    
    # 内存预分配
    print(f"\n内存预分配优化:")
    
    # 不好的做法：重复创建数组
    start_time = time.time()
    result_bad = np.zeros((100, 100))
    for i in range(100):
        temp = np.random.randn(100, 100)
        result_bad += temp
    time_bad = time.time() - start_time
    
    # 好的做法：预分配内存
    start_time = time.time()
    result_good = np.zeros((100, 100))
    temp_array = np.zeros((100, 100))
    for i in range(100):
        np.random.randn(100, 100, out=temp_array)
        result_good += temp_array
    time_good = time.time() - start_time
    
    print(f"重复创建数组时间: {time_bad:.4f}秒")
    print(f"预分配内存时间: {time_good:.4f}秒")
    print(f"性能提升: {time_bad/time_good:.2f}x")

# 主程序演示
if __name__ == "__main__":
    print("NumPy线性代数学习")
    print("=" * 50)
    
    demonstrate_matrix_operations()
    demonstrate_vector_operations()
    demonstrate_eigenvalues_eigenvectors()
    demonstrate_svd()
    demonstrate_solving_linear_systems()
    neural_network_example()
    performance_optimization()
    
    print("\n" + "=" * 50)
    print("NumPy线性代数概念总结:")
    print("✓ 矩阵运算：乘法、转置、逆矩阵、行列式")
    print("✓ 向量运算：点积、叉积、范数、单位向量")
    print("✓ 特征分解：特征值和特征向量的计算")
    print("✓ 奇异值分解：矩阵的SVD分解和低秩近似")
    print("✓ 线性方程组：精确解和最小二乘解")
    print("✓ 神经网络应用：前向传播中的矩阵运算")
    print("✓ 性能优化：高效的矩阵运算技巧")
    print("\n这些线性代数操作是深度学习的数学基础！")
