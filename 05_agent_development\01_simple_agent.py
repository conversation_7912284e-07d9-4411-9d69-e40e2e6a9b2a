# 第1步：最简单的Agent实现 - 理解核心概念
# 这个例子不使用任何框架，纯Python实现Agent的基本逻辑

import json
import re
from typing import List, Dict, Any

class SimpleTool:
    """简单工具的基类"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    def execute(self, input_text: str) -> str:
        """执行工具，子类需要重写这个方法"""
        raise NotImplementedError

class Calculator(SimpleTool):
    """计算器工具"""
    def __init__(self):
        super().__init__(
            name="calculator",
            description="可以进行基本的数学计算，如加减乘除。输入数学表达式，返回计算结果。"
        )
    
    def execute(self, expression: str) -> str:
        try:
            # 简单的表达式求值（实际应用中需要更安全的方法）
            result = eval(expression.strip())
            return f"计算结果: {result}"
        except Exception as e:
            return f"计算错误: {str(e)}"

class SimpleAgent:
    """最简单的Agent实现"""
    
    def __init__(self, tools: List[SimpleTool]):
        self.tools = {tool.name: tool for tool in tools}
        self.conversation_history = []
    
    def get_tools_description(self) -> str:
        """获取所有工具的描述"""
        descriptions = []
        for tool in self.tools.values():
            descriptions.append(f"- {tool.name}: {tool.description}")
        return "\n".join(descriptions)
    
    def simulate_llm_response(self, user_input: str) -> str:
        """
        模拟LLM的响应（实际应用中这里会调用真实的LLM）
        这里用简单的规则来演示Agent的思考过程
        """
        
        # 构建提示词
        tools_desc = self.get_tools_description()
        
        prompt = f"""
你是一个智能助手，可以使用以下工具来帮助用户：

可用工具：
{tools_desc}

对话历史：
{self._format_history()}

用户问题：{user_input}

请按照以下格式回答：
如果需要使用工具：
思考：[你的思考过程]
行动：[工具名称]
输入：[工具输入]

如果不需要工具：
回答：[直接回答用户]

现在请回答：
"""
        
        # 这里模拟LLM的推理过程
        if any(char in user_input for char in ['+', '-', '*', '/', '计算', '算']):
            # 检测到数学计算需求
            math_expression = self._extract_math_expression(user_input)
            return f"""思考：用户想要进行数学计算，我需要使用计算器工具。
行动：calculator
输入：{math_expression}"""
        else:
            return f"回答：你好！我可以帮你进行数学计算。试试问我一个数学问题吧！"
    
    def _extract_math_expression(self, text: str) -> str:
        """从文本中提取数学表达式"""
        # 简单的正则表达式提取数字和运算符
        import re
        pattern = r'[\d+\-*/().\s]+'
        matches = re.findall(pattern, text)
        if matches:
            return ''.join(matches).strip()
        return text
    
    def _format_history(self) -> str:
        """格式化对话历史"""
        if not self.conversation_history:
            return "无"
        
        formatted = []
        for entry in self.conversation_history[-3:]:  # 只显示最近3轮对话
            formatted.append(f"用户：{entry['user']}")
            formatted.append(f"助手：{entry['assistant']}")
        return "\n".join(formatted)
    
    def parse_response(self, response: str) -> Dict[str, Any]:
        """解析LLM的响应"""
        if "行动：" in response and "输入：" in response:
            # 提取工具调用信息
            action_match = re.search(r"行动：(.+)", response)
            input_match = re.search(r"输入：(.+)", response)
            
            if action_match and input_match:
                tool_name = action_match.group(1).strip()
                tool_input = input_match.group(1).strip()
                
                return {
                    "type": "tool_call",
                    "tool_name": tool_name,
                    "tool_input": tool_input,
                    "thinking": response.split("行动：")[0].replace("思考：", "").strip()
                }
        
        # 直接回答
        answer = response.replace("回答：", "").strip()
        return {
            "type": "direct_answer",
            "answer": answer
        }
    
    def execute_tool(self, tool_name: str, tool_input: str) -> str:
        """执行指定的工具"""
        if tool_name in self.tools:
            return self.tools[tool_name].execute(tool_input)
        else:
            return f"错误：未找到工具 '{tool_name}'"
    
    def chat(self, user_input: str) -> str:
        """主要的对话函数"""
        print(f"\n🧠 用户输入：{user_input}")
        
        # 1. 获取LLM响应
        llm_response = self.simulate_llm_response(user_input)
        print(f"🤖 LLM原始响应：\n{llm_response}")
        
        # 2. 解析响应
        parsed = self.parse_response(llm_response)
        
        if parsed["type"] == "tool_call":
            print(f"💭 思考过程：{parsed['thinking']}")
            print(f"🔧 调用工具：{parsed['tool_name']}")
            print(f"📝 工具输入：{parsed['tool_input']}")
            
            # 3. 执行工具
            tool_result = self.execute_tool(parsed['tool_name'], parsed['tool_input'])
            print(f"⚡ 工具结果：{tool_result}")
            
            # 4. 生成最终回答
            final_answer = f"根据计算结果：{tool_result}"
            
        else:
            final_answer = parsed["answer"]
        
        # 5. 保存对话历史
        self.conversation_history.append({
            "user": user_input,
            "assistant": final_answer
        })
        
        print(f"✅ 最终回答：{final_answer}")
        return final_answer

def main():
    """演示简单Agent的使用"""
    print("=== 简单Agent演示 ===")
    print("这个例子展示了Agent的核心工作原理：")
    print("1. 接收用户输入")
    print("2. LLM分析并决定是否使用工具") 
    print("3. 如果需要，调用相应工具")
    print("4. 整合结果并回复用户\n")
    
    # 创建工具
    calculator = Calculator()
    
    # 创建Agent
    agent = SimpleAgent([calculator])
    
    # 测试对话
    test_questions = [
        "你好！",
        "帮我计算 25 * 4",
        "100 - 37 等于多少？",
        "计算 (10 + 5) * 3",
    ]
    
    for question in test_questions:
        print("\n" + "="*50)
        agent.chat(question)
        input("\n按回车键继续下一个测试...")
    
    print("\n🎉 演示完成！")
    print("\n关键理解点：")
    print("- Agent = LLM + Tools + 控制循环")
    print("- LLM负责理解和决策")
    print("- Tools提供具体能力")
    print("- 控制循环协调整个过程")

if __name__ == "__main__":
    main()