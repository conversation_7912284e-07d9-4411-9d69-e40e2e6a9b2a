"""
NumPy基础 - 数组创建、属性、索引
NumPy是Python科学计算的基石，理解NumPy对学习深度学习框架至关重要
"""

import numpy as np
import time

def demonstrate_array_creation():
    """演示数组创建的各种方法"""
    print("=== 数组创建方法 ===")
    
    # 1. 从Python列表创建
    list_1d = [1, 2, 3, 4, 5]
    arr_1d = np.array(list_1d)
    print(f"从列表创建1D数组: {arr_1d}")
    
    list_2d = [[1, 2, 3], [4, 5, 6]]
    arr_2d = np.array(list_2d)
    print(f"从嵌套列表创建2D数组:\n{arr_2d}")
    
    # 2. 使用内置函数创建
    zeros = np.zeros((3, 4))
    ones = np.ones((2, 3))
    full = np.full((2, 2), 7)
    eye = np.eye(3)  # 单位矩阵
    
    print(f"零数组 (3x4):\n{zeros}")
    print(f"单位矩阵 (3x3):\n{eye}")
    
    # 3. 数值范围创建
    arange_arr = np.arange(0, 10, 2)  # 起始、结束、步长
    linspace_arr = np.linspace(0, 1, 5)  # 起始、结束、数量
    
    print(f"arange(0, 10, 2): {arange_arr}")
    print(f"linspace(0, 1, 5): {linspace_arr}")
    
    # 4. 随机数组
    np.random.seed(42)  # 设置随机种子
    random_arr = np.random.random((2, 3))
    normal_arr = np.random.normal(0, 1, (2, 3))  # 正态分布
    
    print(f"随机数组 (0-1):\n{random_arr}")
    print(f"正态分布数组:\n{normal_arr}")

def demonstrate_array_attributes():
    """演示数组属性"""
    print("\n=== 数组属性 ===")
    
    arr = np.random.randint(0, 10, (3, 4, 2))
    
    print(f"数组:\n{arr}")
    print(f"形状 (shape): {arr.shape}")
    print(f"维度数 (ndim): {arr.ndim}")
    print(f"元素总数 (size): {arr.size}")
    print(f"数据类型 (dtype): {arr.dtype}")
    print(f"每个元素字节数 (itemsize): {arr.itemsize}")
    print(f"总字节数 (nbytes): {arr.nbytes}")
    
    # 数据类型转换
    float_arr = arr.astype(np.float32)
    print(f"转换为float32后的dtype: {float_arr.dtype}")

def demonstrate_indexing_slicing():
    """演示索引和切片"""
    print("\n=== 索引和切片 ===")
    
    # 1D数组索引
    arr_1d = np.arange(10)
    print(f"1D数组: {arr_1d}")
    print(f"索引[2]: {arr_1d[2]}")
    print(f"切片[2:7]: {arr_1d[2:7]}")
    print(f"步长切片[::2]: {arr_1d[::2]}")
    print(f"反向切片[::-1]: {arr_1d[::-1]}")
    
    # 2D数组索引
    arr_2d = np.arange(12).reshape(3, 4)
    print(f"\n2D数组:\n{arr_2d}")
    print(f"元素[1, 2]: {arr_2d[1, 2]}")
    print(f"第1行: {arr_2d[1, :]}")
    print(f"第2列: {arr_2d[:, 2]}")
    print(f"子矩阵[0:2, 1:3]:\n{arr_2d[0:2, 1:3]}")
    
    # 布尔索引
    print(f"\n布尔索引:")
    mask = arr_2d > 5
    print(f"大于5的掩码:\n{mask}")
    print(f"大于5的元素: {arr_2d[mask]}")
    
    # 花式索引
    print(f"\n花式索引:")
    indices = [0, 2]
    print(f"选择第0和第2行:\n{arr_2d[indices]}")
    
    row_indices = [0, 1, 2]
    col_indices = [1, 2, 3]
    print(f"选择特定位置的元素: {arr_2d[row_indices, col_indices]}")

def demonstrate_array_operations():
    """演示数组运算"""
    print("\n=== 数组运算 ===")
    
    a = np.array([1, 2, 3, 4])
    b = np.array([5, 6, 7, 8])
    
    print(f"数组a: {a}")
    print(f"数组b: {b}")
    
    # 元素级运算
    print(f"a + b: {a + b}")
    print(f"a * b: {a * b}")  # 元素级乘法
    print(f"a ** 2: {a ** 2}")
    print(f"a > 2: {a > 2}")
    
    # 数学函数
    print(f"sqrt(a): {np.sqrt(a)}")
    print(f"exp(a): {np.exp(a)}")
    print(f"sin(a): {np.sin(a)}")
    
    # 矩阵运算
    matrix_a = np.array([[1, 2], [3, 4]])
    matrix_b = np.array([[5, 6], [7, 8]])
    
    print(f"\n矩阵A:\n{matrix_a}")
    print(f"矩阵B:\n{matrix_b}")
    print(f"矩阵乘法 A @ B:\n{matrix_a @ matrix_b}")
    print(f"矩阵乘法 np.dot(A, B):\n{np.dot(matrix_a, matrix_b)}")

def demonstrate_broadcasting():
    """演示广播机制"""
    print("\n=== 广播机制 ===")
    
    # 标量与数组
    arr = np.array([1, 2, 3, 4])
    scalar = 10
    print(f"数组: {arr}")
    print(f"标量: {scalar}")
    print(f"数组 + 标量: {arr + scalar}")
    
    # 不同形状的数组
    a = np.array([[1, 2, 3],
                  [4, 5, 6]])  # (2, 3)
    b = np.array([10, 20, 30])  # (3,)
    
    print(f"\n数组A (2x3):\n{a}")
    print(f"数组B (3,): {b}")
    print(f"A + B (广播):\n{a + b}")
    
    # 更复杂的广播
    c = np.array([[1], [2]])  # (2, 1)
    print(f"\n数组C (2x1):\n{c}")
    print(f"A + C (广播):\n{a + c}")
    
    # 广播规则演示
    print(f"\n广播规则演示:")
    x = np.random.random((3, 1, 4))
    y = np.random.random((1, 5, 1))
    result = x + y  # 结果形状: (3, 5, 4)
    print(f"x形状: {x.shape}")
    print(f"y形状: {y.shape}")
    print(f"x + y 结果形状: {result.shape}")

def performance_comparison():
    """性能比较：NumPy vs 纯Python"""
    print("\n=== 性能比较 ===")
    
    size = 1000000
    
    # 纯Python实现
    python_list_a = list(range(size))
    python_list_b = list(range(size, 2*size))
    
    start_time = time.time()
    python_result = [a + b for a, b in zip(python_list_a, python_list_b)]
    python_time = time.time() - start_time
    
    # NumPy实现
    numpy_array_a = np.arange(size)
    numpy_array_b = np.arange(size, 2*size)
    
    start_time = time.time()
    numpy_result = numpy_array_a + numpy_array_b
    numpy_time = time.time() - start_time
    
    print(f"数组大小: {size:,}")
    print(f"Python列表时间: {python_time:.4f}秒")
    print(f"NumPy数组时间: {numpy_time:.4f}秒")
    print(f"NumPy加速比: {python_time/numpy_time:.1f}x")
    
    # 验证结果一致性
    print(f"结果一致性: {np.array_equal(python_result, numpy_result)}")

def memory_layout_demo():
    """演示内存布局和视图vs拷贝"""
    print("\n=== 内存布局和视图vs拷贝 ===")
    
    # 原始数组
    original = np.arange(12).reshape(3, 4)
    print(f"原始数组:\n{original}")
    
    # 视图 (view) - 共享内存
    view = original[1:, 1:]
    print(f"视图 (切片):\n{view}")
    
    # 修改视图会影响原数组
    view[0, 0] = 999
    print(f"修改视图后的原数组:\n{original}")
    
    # 拷贝 (copy) - 独立内存
    copy = original.copy()
    copy[0, 0] = 888
    print(f"修改拷贝后的原数组:\n{original}")
    print(f"拷贝数组:\n{copy}")
    
    # 检查是否共享内存
    print(f"view与original共享内存: {np.shares_memory(view, original)}")
    print(f"copy与original共享内存: {np.shares_memory(copy, original)}")

# 主程序演示
if __name__ == "__main__":
    print("NumPy基础学习")
    print("=" * 50)
    
    demonstrate_array_creation()
    demonstrate_array_attributes()
    demonstrate_indexing_slicing()
    demonstrate_array_operations()
    demonstrate_broadcasting()
    performance_comparison()
    memory_layout_demo()
    
    print("\n" + "=" * 50)
    print("NumPy基础概念总结:")
    print("✓ 数组创建：多种方法创建不同类型的数组")
    print("✓ 数组属性：形状、维度、数据类型等重要属性")
    print("✓ 索引切片：灵活的数据访问方式")
    print("✓ 元素运算：向量化操作，避免Python循环")
    print("✓ 广播机制：不同形状数组的运算规则")
    print("✓ 性能优势：比纯Python快数十倍")
    print("✓ 内存管理：理解视图和拷贝的区别")
    print("\n准备好学习更高级的NumPy操作了！")
