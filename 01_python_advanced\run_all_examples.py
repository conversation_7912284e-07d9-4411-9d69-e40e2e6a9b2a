"""
运行所有Python高级特性示例
这个脚本会依次运行所有的示例代码，帮助你理解每个概念
"""

import sys
import os
import importlib.util

def run_module(module_path, module_name):
    """动态运行模块"""
    print(f"\n{'='*60}")
    print(f"运行 {module_name}")
    print(f"{'='*60}")
    
    try:
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print(f"\n✓ {module_name} 运行成功!")
        
    except Exception as e:
        print(f"\n✗ {module_name} 运行失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("Python高级特性学习 - 示例代码运行器")
    print("这将帮助你逐步理解每个概念")
    
    # 要运行的模块列表
    modules = [
        ("01_oop_advanced.py", "面向对象编程进阶"),
        ("02_decorators.py", "装饰器"),
        ("03_generators_iterators.py", "生成器和迭代器"),
        ("04_context_managers.py", "上下文管理器"),
        ("05_functional_programming.py", "函数式编程"),
        ("practice_project.py", "综合实践项目")
    ]
    
    success_count = 0
    total_count = len(modules)
    
    for module_file, module_desc in modules:
        module_path = os.path.join(os.path.dirname(__file__), module_file)
        
        if os.path.exists(module_path):
            if run_module(module_path, module_desc):
                success_count += 1
        else:
            print(f"\n✗ 文件不存在: {module_file}")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"运行总结")
    print(f"{'='*60}")
    print(f"成功运行: {success_count}/{total_count} 个模块")
    
    if success_count == total_count:
        print("\n🎉 恭喜！所有示例都运行成功！")
        print("你已经掌握了Python的高级特性，可以进入下一阶段的学习了。")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个模块运行失败，请检查代码。")
    
    print("\n下一步学习建议:")
    print("1. 仔细阅读每个示例的代码和注释")
    print("2. 尝试修改代码，观察运行结果的变化")
    print("3. 完成practice_project.py中的练习")
    print("4. 准备进入数学基础学习阶段")

if __name__ == "__main__":
    main()
