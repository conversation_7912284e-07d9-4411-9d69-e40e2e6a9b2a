"""
装饰器学习 - 为机器学习代码添加功能
装饰器在深度学习中非常有用，比如计时、缓存、参数验证等
"""

import time
import functools
from typing import Callable, Any

# 1. 基础装饰器
def timer(func):
    """计时装饰器 - 测量函数执行时间"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        return result
    return wrapper

# 2. 带参数的装饰器
def validate_inputs(input_type=None, min_length=None):
    """输入验证装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 验证第一个参数（通常是输入数据）
            if args and input_type:
                if not isinstance(args[0], input_type):
                    raise TypeError(f"期望输入类型 {input_type}，得到 {type(args[0])}")
            
            if args and min_length and hasattr(args[0], '__len__'):
                if len(args[0]) < min_length:
                    raise ValueError(f"输入长度至少为 {min_length}，得到 {len(args[0])}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

# 3. 类装饰器
class Cache:
    """缓存装饰器类 - 用于缓存计算结果"""
    
    def __init__(self, maxsize=128):
        self.maxsize = maxsize
        self.cache = {}
        self.access_order = []
    
    def __call__(self, func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(sorted(kwargs.items()))
            
            if key in self.cache:
                # 更新访问顺序
                self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key]
            
            # 计算结果
            result = func(*args, **kwargs)
            
            # 添加到缓存
            if len(self.cache) >= self.maxsize:
                # 移除最久未使用的项
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[key] = result
            self.access_order.append(key)
            return result
        
        wrapper.cache_info = lambda: {
            'size': len(self.cache),
            'maxsize': self.maxsize,
            'keys': list(self.cache.keys())
        }
        wrapper.cache_clear = lambda: self.cache.clear() or self.access_order.clear()
        
        return wrapper

# 4. 属性装饰器用于机器学习
class MLModel:
    """机器学习模型基类 - 演示属性装饰器"""
    
    def __init__(self):
        self._parameters = {}
        self._training = True
    
    @property
    def training(self):
        """训练模式属性"""
        return self._training
    
    @training.setter
    def training(self, value):
        """设置训练模式"""
        if not isinstance(value, bool):
            raise TypeError("training必须是布尔值")
        self._training = value
        print(f"模型切换到 {'训练' if value else '评估'} 模式")
    
    @property
    def num_parameters(self):
        """只读属性 - 参数数量"""
        return sum(len(param) if hasattr(param, '__len__') else 1 
                  for param in self._parameters.values())
    
    def add_parameter(self, name, value):
        """添加参数"""
        self._parameters[name] = value

# 应用示例
@timer
@validate_inputs(input_type=list, min_length=2)
def matrix_multiply_simple(matrix_a, matrix_b):
    """简单矩阵乘法 - 演示装饰器组合"""
    if len(matrix_a[0]) != len(matrix_b):
        raise ValueError("矩阵维度不匹配")
    
    result = []
    for i in range(len(matrix_a)):
        row = []
        for j in range(len(matrix_b[0])):
            sum_val = 0
            for k in range(len(matrix_b)):
                sum_val += matrix_a[i][k] * matrix_b[k][j]
            row.append(sum_val)
        result.append(row)
    return result

@Cache(maxsize=64)
def fibonacci(n):
    """斐波那契数列 - 演示缓存装饰器"""
    if n < 2:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 演示使用
if __name__ == "__main__":
    print("=== 装饰器演示 ===")
    
    # 1. 计时和验证装饰器
    matrix_a = [[1, 2], [3, 4]]
    matrix_b = [[5, 6], [7, 8]]
    
    result = matrix_multiply_simple(matrix_a, matrix_b)
    print(f"矩阵乘法结果: {result}")
    
    # 2. 缓存装饰器
    print(f"\n斐波那契数列:")
    for i in range(10):
        print(f"fib({i}) = {fibonacci(i)}")
    
    print(f"缓存信息: {fibonacci.cache_info()}")
    
    # 3. 属性装饰器
    print(f"\n=== 模型属性演示 ===")
    model = MLModel()
    model.add_parameter("weights", [1, 2, 3, 4])
    model.add_parameter("bias", 0.5)
    
    print(f"参数数量: {model.num_parameters}")
    print(f"当前模式: {'训练' if model.training else '评估'}")
    
    model.training = False
    model.training = True
