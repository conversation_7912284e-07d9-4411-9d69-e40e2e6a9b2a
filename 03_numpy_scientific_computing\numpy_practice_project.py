"""
NumPy综合实践项目 - 从零实现一个简单的神经网络
综合运用NumPy的所有核心功能，为学习深度学习框架做准备
"""

import numpy as np
import time

class NeuralNetwork:
    """使用纯NumPy实现的神经网络"""
    
    def __init__(self, layer_sizes, activation='relu', learning_rate=0.01):
        """
        初始化神经网络
        
        Args:
            layer_sizes: 每层的神经元数量，如[784, 128, 64, 10]
            activation: 激活函数类型
            learning_rate: 学习率
        """
        self.layer_sizes = layer_sizes
        self.num_layers = len(layer_sizes)
        self.learning_rate = learning_rate
        self.activation = activation
        
        # 初始化权重和偏置
        self.weights = []
        self.biases = []
        
        for i in range(self.num_layers - 1):
            # He初始化（适用于ReLU）
            w = np.random.randn(layer_sizes[i], layer_sizes[i+1]) * np.sqrt(2.0 / layer_sizes[i])
            b = np.zeros((1, layer_sizes[i+1]))
            
            self.weights.append(w)
            self.biases.append(b)
        
        # 训练历史
        self.loss_history = []
        self.accuracy_history = []
    
    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def relu_derivative(self, x):
        """ReLU导数"""
        return (x > 0).astype(float)
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        # 数值稳定性处理
        x = np.clip(x, -500, 500)
        return 1 / (1 + np.exp(-x))
    
    def sigmoid_derivative(self, x):
        """Sigmoid导数"""
        s = self.sigmoid(x)
        return s * (1 - s)
    
    def softmax(self, x):
        """Softmax激活函数"""
        # 数值稳定性：减去最大值
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)
    
    def forward(self, X):
        """前向传播"""
        self.activations = [X]  # 存储每层的激活值
        self.z_values = []      # 存储每层的线性输出
        
        current_input = X
        
        for i in range(self.num_layers - 1):
            # 线性变换: z = X @ W + b
            z = current_input @ self.weights[i] + self.biases[i]
            self.z_values.append(z)
            
            # 激活函数
            if i == self.num_layers - 2:  # 输出层使用softmax
                a = self.softmax(z)
            else:  # 隐藏层使用指定激活函数
                if self.activation == 'relu':
                    a = self.relu(z)
                elif self.activation == 'sigmoid':
                    a = self.sigmoid(z)
                else:
                    raise ValueError(f"不支持的激活函数: {self.activation}")
            
            self.activations.append(a)
            current_input = a
        
        return self.activations[-1]
    
    def compute_loss(self, y_pred, y_true):
        """计算交叉熵损失"""
        # 避免log(0)
        y_pred = np.clip(y_pred, 1e-15, 1 - 1e-15)
        
        # 交叉熵损失
        loss = -np.mean(np.sum(y_true * np.log(y_pred), axis=1))
        return loss
    
    def backward(self, X, y_true):
        """反向传播"""
        m = X.shape[0]  # 批次大小
        
        # 输出层误差
        y_pred = self.activations[-1]
        delta = y_pred - y_true
        
        # 存储梯度
        weight_gradients = []
        bias_gradients = []
        
        # 从输出层向输入层反向传播
        for i in range(self.num_layers - 2, -1, -1):
            # 计算权重和偏置梯度
            dW = self.activations[i].T @ delta / m
            db = np.mean(delta, axis=0, keepdims=True)
            
            weight_gradients.insert(0, dW)
            bias_gradients.insert(0, db)
            
            # 计算前一层的误差（如果不是输入层）
            if i > 0:
                delta = (delta @ self.weights[i].T) * self.get_activation_derivative(self.z_values[i-1])
        
        return weight_gradients, bias_gradients
    
    def get_activation_derivative(self, z):
        """获取激活函数的导数"""
        if self.activation == 'relu':
            return self.relu_derivative(z)
        elif self.activation == 'sigmoid':
            return self.sigmoid_derivative(z)
        else:
            raise ValueError(f"不支持的激活函数: {self.activation}")
    
    def update_parameters(self, weight_gradients, bias_gradients):
        """更新参数"""
        for i in range(len(self.weights)):
            self.weights[i] -= self.learning_rate * weight_gradients[i]
            self.biases[i] -= self.learning_rate * bias_gradients[i]
    
    def train(self, X, y, epochs=100, batch_size=32, validation_data=None, verbose=True):
        """训练神经网络"""
        n_samples = X.shape[0]
        
        for epoch in range(epochs):
            # 打乱数据
            indices = np.random.permutation(n_samples)
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            # 小批次训练
            epoch_loss = 0
            num_batches = 0
            
            for i in range(0, n_samples, batch_size):
                # 获取批次数据
                end_idx = min(i + batch_size, n_samples)
                X_batch = X_shuffled[i:end_idx]
                y_batch = y_shuffled[i:end_idx]
                
                # 前向传播
                y_pred = self.forward(X_batch)
                
                # 计算损失
                batch_loss = self.compute_loss(y_pred, y_batch)
                epoch_loss += batch_loss
                num_batches += 1
                
                # 反向传播
                weight_grads, bias_grads = self.backward(X_batch, y_batch)
                
                # 更新参数
                self.update_parameters(weight_grads, bias_grads)
            
            # 记录训练历史
            avg_loss = epoch_loss / num_batches
            self.loss_history.append(avg_loss)
            
            # 计算准确率
            train_accuracy = self.evaluate(X, y)
            self.accuracy_history.append(train_accuracy)
            
            # 验证集评估
            val_accuracy = None
            if validation_data is not None:
                X_val, y_val = validation_data
                val_accuracy = self.evaluate(X_val, y_val)
            
            # 打印进度
            if verbose and (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{epochs}")
                print(f"  训练损失: {avg_loss:.4f}")
                print(f"  训练准确率: {train_accuracy:.4f}")
                if val_accuracy is not None:
                    print(f"  验证准确率: {val_accuracy:.4f}")
                print()
    
    def predict(self, X):
        """预测"""
        y_pred = self.forward(X)
        return np.argmax(y_pred, axis=1)
    
    def predict_proba(self, X):
        """预测概率"""
        return self.forward(X)
    
    def evaluate(self, X, y):
        """评估准确率"""
        y_pred = self.predict(X)
        y_true = np.argmax(y, axis=1)
        accuracy = np.mean(y_pred == y_true)
        return accuracy

class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def generate_classification_data(n_samples=1000, n_features=20, n_classes=3, noise=0.1):
        """生成分类数据集"""
        np.random.seed(42)
        
        # 为每个类别生成中心点
        class_centers = np.random.randn(n_classes, n_features) * 2
        
        X = []
        y = []
        
        samples_per_class = n_samples // n_classes
        
        for class_idx in range(n_classes):
            # 围绕类别中心生成数据
            class_data = np.random.randn(samples_per_class, n_features) * noise + class_centers[class_idx]
            class_labels = np.full(samples_per_class, class_idx)
            
            X.append(class_data)
            y.append(class_labels)
        
        X = np.vstack(X)
        y = np.hstack(y)
        
        # 打乱数据
        indices = np.random.permutation(len(X))
        X = X[indices]
        y = y[indices]
        
        return X, y
    
    @staticmethod
    def train_test_split(X, y, test_size=0.2):
        """划分训练集和测试集"""
        n_samples = len(X)
        n_test = int(n_samples * test_size)
        
        indices = np.random.permutation(n_samples)
        test_indices = indices[:n_test]
        train_indices = indices[n_test:]
        
        X_train, X_test = X[train_indices], X[test_indices]
        y_train, y_test = y[train_indices], y[test_indices]
        
        return X_train, X_test, y_train, y_test
    
    @staticmethod
    def standardize(X_train, X_test=None):
        """标准化数据"""
        mean = np.mean(X_train, axis=0)
        std = np.std(X_train, axis=0)
        
        X_train_scaled = (X_train - mean) / (std + 1e-8)
        
        if X_test is not None:
            X_test_scaled = (X_test - mean) / (std + 1e-8)
            return X_train_scaled, X_test_scaled
        
        return X_train_scaled
    
    @staticmethod
    def to_categorical(y, num_classes=None):
        """转换为one-hot编码"""
        if num_classes is None:
            num_classes = len(np.unique(y))
        
        categorical = np.zeros((len(y), num_classes))
        categorical[np.arange(len(y)), y] = 1
        
        return categorical

def performance_comparison():
    """性能比较：NumPy vs 纯Python"""
    print("=== 性能比较 ===")
    
    # 矩阵乘法性能比较
    size = 500
    A = np.random.randn(size, size)
    B = np.random.randn(size, size)
    
    # NumPy矩阵乘法
    start_time = time.time()
    C_numpy = A @ B
    numpy_time = time.time() - start_time
    
    # 纯Python矩阵乘法（简化版，只计算部分元素）
    def python_matmul_sample(A, B, sample_size=100):
        """纯Python矩阵乘法（采样版本）"""
        result = []
        for i in range(min(sample_size, A.shape[0])):
            row = []
            for j in range(min(sample_size, B.shape[1])):
                element = sum(A[i][k] * B[k][j] for k in range(A.shape[1]))
                row.append(element)
            result.append(row)
        return result
    
    start_time = time.time()
    C_python = python_matmul_sample(A.tolist(), B.tolist())
    python_time = time.time() - start_time
    
    print(f"矩阵大小: {size}x{size}")
    print(f"NumPy时间: {numpy_time:.4f}秒")
    print(f"Python时间 (100x100采样): {python_time:.4f}秒")
    print(f"估计加速比: {python_time * (size/100)**2 / numpy_time:.1f}x")

def main():
    """主函数"""
    print("NumPy综合实践项目 - 神经网络实现")
    print("=" * 60)
    
    # 1. 生成数据
    print("1. 生成数据集...")
    X, y = DataProcessor.generate_classification_data(
        n_samples=2000, n_features=50, n_classes=5, noise=0.5
    )
    
    print(f"数据集形状: {X.shape}")
    print(f"类别分布: {np.bincount(y)}")
    
    # 2. 数据预处理
    print("\n2. 数据预处理...")
    X_train, X_test, y_train, y_test = DataProcessor.train_test_split(X, y, test_size=0.2)
    X_train, X_test = DataProcessor.standardize(X_train, X_test)
    
    y_train_cat = DataProcessor.to_categorical(y_train, num_classes=5)
    y_test_cat = DataProcessor.to_categorical(y_test, num_classes=5)
    
    print(f"训练集形状: {X_train.shape}")
    print(f"测试集形状: {X_test.shape}")
    print(f"标签形状: {y_train_cat.shape}")
    
    # 3. 创建和训练模型
    print("\n3. 创建神经网络...")
    model = NeuralNetwork(
        layer_sizes=[50, 128, 64, 32, 5],
        activation='relu',
        learning_rate=0.01
    )
    
    print("网络结构:")
    for i, size in enumerate(model.layer_sizes):
        if i == 0:
            print(f"  输入层: {size} 个神经元")
        elif i == len(model.layer_sizes) - 1:
            print(f"  输出层: {size} 个神经元")
        else:
            print(f"  隐藏层{i}: {size} 个神经元")
    
    total_params = sum(w.size for w in model.weights) + sum(b.size for b in model.biases)
    print(f"总参数数量: {total_params:,}")
    
    # 4. 训练模型
    print("\n4. 训练模型...")
    start_time = time.time()
    
    model.train(
        X_train, y_train_cat,
        epochs=100,
        batch_size=32,
        validation_data=(X_test, y_test_cat),
        verbose=True
    )
    
    training_time = time.time() - start_time
    print(f"训练时间: {training_time:.2f}秒")
    
    # 5. 模型评估
    print("\n5. 模型评估...")
    train_accuracy = model.evaluate(X_train, y_train_cat)
    test_accuracy = model.evaluate(X_test, y_test_cat)
    
    print(f"最终训练准确率: {train_accuracy:.4f}")
    print(f"最终测试准确率: {test_accuracy:.4f}")
    
    # 6. 预测示例
    print("\n6. 预测示例...")
    sample_indices = np.random.choice(len(X_test), 5, replace=False)
    
    for i, idx in enumerate(sample_indices):
        sample_x = X_test[idx:idx+1]
        true_label = np.argmax(y_test_cat[idx])
        pred_proba = model.predict_proba(sample_x)[0]
        pred_label = np.argmax(pred_proba)
        
        print(f"样本 {i+1}:")
        print(f"  真实标签: {true_label}")
        print(f"  预测标签: {pred_label}")
        print(f"  预测概率: {pred_proba}")
        print(f"  预测正确: {'✓' if pred_label == true_label else '✗'}")
        print()
    
    # 7. 性能比较
    performance_comparison()
    
    print("\n" + "=" * 60)
    print("项目总结:")
    print("✓ 数据生成：使用NumPy生成分类数据集")
    print("✓ 数据预处理：标准化、one-hot编码、数据分割")
    print("✓ 神经网络：完整的前向传播和反向传播")
    print("✓ 矩阵运算：高效的线性代数计算")
    print("✓ 向量化：避免Python循环，提升性能")
    print("✓ 广播机制：灵活的数组运算")
    print("✓ 随机数：参数初始化和数据打乱")
    print("✓ 统计分析：损失和准确率计算")
    print("\n恭喜！你已经掌握了NumPy的核心功能！")

if __name__ == "__main__":
    main()
