"""
数学基础综合练习项目
结合线性代数、微积分、概率论，实现一个简单的机器学习算法
"""

import math
import random
from typing import List, Tuple, Callable

# 导入我们之前实现的类
from typing import List, Tuple

class Vector:
    """向量类（简化版）"""
    def __init__(self, components: List[float]):
        self.components = components
        self.dim = len(components)
    
    def __add__(self, other):
        return Vector([a + b for a, b in zip(self.components, other.components)])
    
    def __mul__(self, scalar: float):
        return Vector([scalar * x for x in self.components])
    
    def dot(self, other) -> float:
        return sum(a * b for a, b in zip(self.components, other.components))
    
    def magnitude(self) -> float:
        return math.sqrt(sum(x**2 for x in self.components))

class Matrix:
    """矩阵类（简化版）"""
    def __init__(self, data: List[List[float]]):
        self.data = data
        self.rows = len(data)
        self.cols = len(data[0]) if data else 0
    
    def __mul__(self, other):
        if isinstance(other, Vector):
            # 矩阵向量乘法
            result = []
            for i in range(self.rows):
                element = sum(self.data[i][j] * other.components[j] for j in range(self.cols))
                result.append(element)
            return Vector(result)
        elif isinstance(other, Matrix):
            # 矩阵乘法
            result = []
            for i in range(self.rows):
                row = []
                for j in range(other.cols):
                    element = sum(self.data[i][k] * other.data[k][j] for k in range(self.cols))
                    row.append(element)
                result.append(row)
            return Matrix(result)

# 1. 逻辑回归实现 - 综合数学概念
class LogisticRegression:
    """逻辑回归 - 综合线性代数、微积分、概率论"""
    
    def __init__(self, learning_rate: float = 0.01, max_iterations: int = 1000):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.weights = None
        self.bias = 0.0
        self.cost_history = []
    
    def sigmoid(self, z: float) -> float:
        """Sigmoid激活函数 - 概率论应用"""
        # 数值稳定性处理
        z = max(-500, min(500, z))
        return 1 / (1 + math.exp(-z))
    
    def predict_proba(self, X: List[List[float]]) -> List[float]:
        """预测概率 - 线性代数 + 概率论"""
        probabilities = []
        for x in X:
            # 线性组合：w·x + b
            linear_combination = sum(w * xi for w, xi in zip(self.weights, x)) + self.bias
            # Sigmoid变换得到概率
            prob = self.sigmoid(linear_combination)
            probabilities.append(prob)
        return probabilities
    
    def cost_function(self, X: List[List[float]], y: List[int]) -> float:
        """交叉熵损失函数 - 信息论应用"""
        m = len(X)
        probabilities = self.predict_proba(X)
        
        cost = 0
        for i in range(m):
            p = probabilities[i]
            # 避免log(0)
            p = max(1e-15, min(1-1e-15, p))
            
            if y[i] == 1:
                cost -= math.log(p)
            else:
                cost -= math.log(1 - p)
        
        return cost / m
    
    def compute_gradients(self, X: List[List[float]], y: List[int]) -> Tuple[List[float], float]:
        """计算梯度 - 微积分应用"""
        m = len(X)
        n_features = len(X[0])
        
        probabilities = self.predict_proba(X)
        
        # 权重梯度
        dw = [0.0] * n_features
        for i in range(m):
            error = probabilities[i] - y[i]
            for j in range(n_features):
                dw[j] += error * X[i][j]
        dw = [gradient / m for gradient in dw]
        
        # 偏置梯度
        db = sum(probabilities[i] - y[i] for i in range(m)) / m
        
        return dw, db
    
    def fit(self, X: List[List[float]], y: List[int]):
        """训练模型 - 梯度下降优化"""
        m, n_features = len(X), len(X[0])
        
        # 初始化参数
        self.weights = [random.uniform(-0.1, 0.1) for _ in range(n_features)]
        self.bias = 0.0
        self.cost_history = []
        
        # 梯度下降
        for iteration in range(self.max_iterations):
            # 计算损失
            cost = self.cost_function(X, y)
            self.cost_history.append(cost)
            
            # 计算梯度
            dw, db = self.compute_gradients(X, y)
            
            # 更新参数
            for i in range(n_features):
                self.weights[i] -= self.learning_rate * dw[i]
            self.bias -= self.learning_rate * db
            
            # 每100次迭代打印一次
            if iteration % 100 == 0:
                print(f"迭代 {iteration}, 损失: {cost:.6f}")
    
    def predict(self, X: List[List[float]]) -> List[int]:
        """预测类别"""
        probabilities = self.predict_proba(X)
        return [1 if p >= 0.5 else 0 for p in probabilities]

# 2. 数据生成器 - 概率论应用
class DataGenerator:
    """生成分类数据集"""
    
    @staticmethod
    def generate_linear_separable_data(n_samples: int = 100, n_features: int = 2, 
                                     noise: float = 0.1) -> Tuple[List[List[float]], List[int]]:
        """生成线性可分数据"""
        X = []
        y = []
        
        # 真实的分界线参数
        true_weights = [random.uniform(-1, 1) for _ in range(n_features)]
        true_bias = random.uniform(-0.5, 0.5)
        
        for _ in range(n_samples):
            # 生成随机特征
            features = [random.uniform(-2, 2) for _ in range(n_features)]
            
            # 计算真实标签（加噪声）
            linear_combination = sum(w * x for w, x in zip(true_weights, features)) + true_bias
            linear_combination += random.gauss(0, noise)
            
            label = 1 if linear_combination > 0 else 0
            
            X.append(features)
            y.append(label)
        
        return X, y

# 3. 模型评估 - 统计学应用
class ModelEvaluator:
    """模型评估工具"""
    
    @staticmethod
    def accuracy(y_true: List[int], y_pred: List[int]) -> float:
        """准确率"""
        correct = sum(1 for true, pred in zip(y_true, y_pred) if true == pred)
        return correct / len(y_true)
    
    @staticmethod
    def confusion_matrix(y_true: List[int], y_pred: List[int]) -> Tuple[int, int, int, int]:
        """混淆矩阵：(TP, FP, TN, FN)"""
        tp = sum(1 for true, pred in zip(y_true, y_pred) if true == 1 and pred == 1)
        fp = sum(1 for true, pred in zip(y_true, y_pred) if true == 0 and pred == 1)
        tn = sum(1 for true, pred in zip(y_true, y_pred) if true == 0 and pred == 0)
        fn = sum(1 for true, pred in zip(y_true, y_pred) if true == 1 and pred == 0)
        return tp, fp, tn, fn
    
    @staticmethod
    def precision_recall_f1(y_true: List[int], y_pred: List[int]) -> Tuple[float, float, float]:
        """精确率、召回率、F1分数"""
        tp, fp, tn, fn = ModelEvaluator.confusion_matrix(y_true, y_pred)
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return precision, recall, f1

# 4. 数学概念验证
class MathConceptVerification:
    """验证数学概念的正确性"""
    
    @staticmethod
    def verify_gradient_computation(model: LogisticRegression, X: List[List[float]], y: List[int]):
        """数值验证梯度计算的正确性"""
        print("验证梯度计算...")
        
        # 解析梯度
        analytical_dw, analytical_db = model.compute_gradients(X, y)
        
        # 数值梯度
        h = 1e-7
        numerical_dw = []
        
        # 验证权重梯度
        for i in range(len(model.weights)):
            # w[i] + h
            model.weights[i] += h
            cost_plus = model.cost_function(X, y)
            
            # w[i] - h
            model.weights[i] -= 2 * h
            cost_minus = model.cost_function(X, y)
            
            # 恢复原值
            model.weights[i] += h
            
            # 数值梯度
            numerical_gradient = (cost_plus - cost_minus) / (2 * h)
            numerical_dw.append(numerical_gradient)
        
        # 验证偏置梯度
        model.bias += h
        cost_plus = model.cost_function(X, y)
        model.bias -= 2 * h
        cost_minus = model.cost_function(X, y)
        model.bias += h
        numerical_db = (cost_plus - cost_minus) / (2 * h)
        
        # 比较结果
        print("权重梯度比较:")
        for i, (analytical, numerical) in enumerate(zip(analytical_dw, numerical_dw)):
            error = abs(analytical - numerical)
            print(f"  w[{i}]: 解析={analytical:.8f}, 数值={numerical:.8f}, 误差={error:.8f}")
        
        error_bias = abs(analytical_db - numerical_db)
        print(f"偏置梯度比较: 解析={analytical_db:.8f}, 数值={numerical_db:.8f}, 误差={error_bias:.8f}")

# 主程序演示
if __name__ == "__main__":
    print("=== 数学基础综合练习项目 ===")
    
    # 设置随机种子
    random.seed(42)
    
    # 1. 生成数据
    print("1. 生成训练数据...")
    X_train, y_train = DataGenerator.generate_linear_separable_data(
        n_samples=200, n_features=2, noise=0.2
    )
    
    X_test, y_test = DataGenerator.generate_linear_separable_data(
        n_samples=50, n_features=2, noise=0.2
    )
    
    print(f"训练集大小: {len(X_train)}")
    print(f"测试集大小: {len(X_test)}")
    print(f"特征维度: {len(X_train[0])}")
    
    # 2. 创建和训练模型
    print("\n2. 训练逻辑回归模型...")
    model = LogisticRegression(learning_rate=0.1, max_iterations=500)
    model.fit(X_train, y_train)
    
    print(f"最终权重: {[f'{w:.4f}' for w in model.weights]}")
    print(f"最终偏置: {model.bias:.4f}")
    
    # 3. 验证梯度计算
    print("\n3. 验证梯度计算...")
    MathConceptVerification.verify_gradient_computation(model, X_train[:10], y_train[:10])
    
    # 4. 模型评估
    print("\n4. 模型评估...")
    
    # 训练集性能
    train_pred = model.predict(X_train)
    train_accuracy = ModelEvaluator.accuracy(y_train, train_pred)
    
    # 测试集性能
    test_pred = model.predict(X_test)
    test_accuracy = ModelEvaluator.accuracy(y_test, test_pred)
    
    print(f"训练集准确率: {train_accuracy:.4f}")
    print(f"测试集准确率: {test_accuracy:.4f}")
    
    # 详细评估指标
    precision, recall, f1 = ModelEvaluator.precision_recall_f1(y_test, test_pred)
    print(f"精确率: {precision:.4f}")
    print(f"召回率: {recall:.4f}")
    print(f"F1分数: {f1:.4f}")
    
    # 5. 概率预测示例
    print("\n5. 概率预测示例...")
    test_probabilities = model.predict_proba(X_test[:5])
    
    for i in range(5):
        features = X_test[i]
        true_label = y_test[i]
        pred_prob = test_probabilities[i]
        pred_label = test_pred[i]
        
        print(f"样本 {i+1}: 特征={[f'{x:.3f}' for x in features]}, "
              f"真实标签={true_label}, 预测概率={pred_prob:.4f}, 预测标签={pred_label}")
    
    # 6. 损失函数收敛曲线
    print("\n6. 训练过程分析...")
    print(f"初始损失: {model.cost_history[0]:.6f}")
    print(f"最终损失: {model.cost_history[-1]:.6f}")
    print(f"损失下降: {model.cost_history[0] - model.cost_history[-1]:.6f}")
    
    # 检查收敛
    last_10_costs = model.cost_history[-10:]
    cost_variance = sum((c - sum(last_10_costs)/10)**2 for c in last_10_costs) / 10
    print(f"最后10次迭代损失方差: {cost_variance:.8f}")
    
    if cost_variance < 1e-8:
        print("✓ 模型已收敛")
    else:
        print("⚠ 模型可能未完全收敛")
    
    print("\n=== 项目总结 ===")
    print("✓ 线性代数：矩阵向量运算、点积计算")
    print("✓ 微积分：梯度计算、数值验证、优化算法")
    print("✓ 概率论：Sigmoid函数、概率预测、交叉熵损失")
    print("✓ 统计学：模型评估、性能指标、收敛分析")
    print("✓ 信息论：交叉熵损失函数")
    print("\n恭喜！你已经掌握了机器学习的数学基础！")
