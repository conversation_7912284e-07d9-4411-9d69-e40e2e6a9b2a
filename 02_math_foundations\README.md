# 机器学习数学基础

## 学习目标
掌握机器学习和深度学习所需的核心数学概念，为理解Transformer架构打下基础。

## 学习内容

### 1. 线性代数 (Linear Algebra)
- **向量和向量运算**
  - 向量的定义、加法、数乘
  - 点积（内积）和向量长度
  - 向量的几何意义
- **矩阵和矩阵运算**
  - 矩阵的定义和基本运算
  - 矩阵乘法的几何意义
  - 转置、逆矩阵
- **特征值和特征向量**
  - 定义和计算方法
  - 在机器学习中的应用
- **奇异值分解 (SVD)**
  - 基本概念和应用

### 2. 微积分 (Calculus)
- **导数和偏导数**
  - 单变量函数的导数
  - 多变量函数的偏导数
  - 链式法则
- **梯度和方向导数**
  - 梯度的定义和几何意义
  - 梯度下降算法的数学基础
- **多元函数优化**
  - 极值的判定
  - 拉格朗日乘数法

### 3. 概率论和统计 (Probability & Statistics)
- **基础概率概念**
  - 概率的定义和性质
  - 条件概率和贝叶斯定理
- **随机变量和分布**
  - 离散和连续随机变量
  - 常见分布（正态分布、伯努利分布等）
- **期望、方差和协方差**
  - 数学期望的计算
  - 方差和标准差
  - 协方差和相关系数

### 4. 信息论基础
- **熵和交叉熵**
  - 信息熵的定义
  - 交叉熵损失函数
- **KL散度**
  - 定义和性质
  - 在机器学习中的应用

## 实践项目
1. 用Python实现基本的线性代数运算
2. 实现梯度下降算法
3. 计算不同分布的概率和统计量
4. 实现信息熵和交叉熵计算

## 学习时间
预计 5-7 天

## 重点关注
- **向量和矩阵运算** - Transformer中大量使用
- **梯度计算** - 反向传播的基础
- **概率分布** - 注意力机制的数学基础
- **信息论** - 损失函数的理论基础

## 下一步
完成后进入NumPy科学计算学习阶段
