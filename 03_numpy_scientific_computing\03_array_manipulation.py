"""
NumPy数组操作 - 形状变换、合并分割、统计聚合
这些操作在数据预处理和神经网络中经常使用
"""

import numpy as np

def demonstrate_shape_manipulation():
    """演示数组形状操作"""
    print("=== 数组形状操作 ===")
    
    # 创建原始数组
    arr = np.arange(24)
    print(f"原始数组: {arr}")
    print(f"原始形状: {arr.shape}")
    
    # reshape - 改变形状
    reshaped_2d = arr.reshape(4, 6)
    reshaped_3d = arr.reshape(2, 3, 4)
    
    print(f"\nreshape(4, 6):\n{reshaped_2d}")
    print(f"reshape(2, 3, 4):\n{reshaped_3d}")
    
    # 自动推断维度
    auto_reshape = arr.reshape(4, -1)  # -1表示自动计算
    print(f"reshape(4, -1):\n{auto_reshape}")
    
    # flatten vs ravel
    flattened = reshaped_2d.flatten()  # 返回拷贝
    raveled = reshaped_2d.ravel()      # 返回视图（如果可能）
    
    print(f"\nflatten(): {flattened}")
    print(f"ravel(): {raveled}")
    
    # 修改测试
    raveled[0] = 999
    print(f"修改ravel后的原数组:\n{reshaped_2d}")
    
    # 转置
    print(f"\n转置操作:")
    matrix = np.arange(12).reshape(3, 4)
    print(f"原矩阵 (3x4):\n{matrix}")
    print(f"转置 .T:\n{matrix.T}")
    print(f"transpose():\n{np.transpose(matrix)}")
    
    # 多维转置
    arr_3d = np.arange(24).reshape(2, 3, 4)
    transposed_3d = np.transpose(arr_3d, (2, 0, 1))  # 轴重排
    print(f"\n3D数组形状: {arr_3d.shape}")
    print(f"轴重排后形状: {transposed_3d.shape}")

def demonstrate_dimension_operations():
    """演示维度操作"""
    print("\n=== 维度操作 ===")
    
    arr = np.array([1, 2, 3, 4])
    print(f"原数组: {arr}, 形状: {arr.shape}")
    
    # 增加维度
    expanded_axis0 = np.expand_dims(arr, axis=0)
    expanded_axis1 = np.expand_dims(arr, axis=1)
    
    print(f"expand_dims(axis=0): {expanded_axis0}, 形状: {expanded_axis0.shape}")
    print(f"expand_dims(axis=1):\n{expanded_axis1}, 形状: {expanded_axis1.shape}")
    
    # 使用newaxis
    newaxis_0 = arr[np.newaxis, :]
    newaxis_1 = arr[:, np.newaxis]
    
    print(f"arr[newaxis, :]: {newaxis_0}, 形状: {newaxis_0.shape}")
    print(f"arr[:, newaxis]:\n{newaxis_1}, 形状: {newaxis_1.shape}")
    
    # 压缩维度
    arr_with_ones = np.array([[[1, 2, 3, 4]]])  # 形状: (1, 1, 4)
    squeezed = np.squeeze(arr_with_ones)
    
    print(f"\n原数组形状: {arr_with_ones.shape}")
    print(f"squeeze后形状: {squeezed.shape}")
    print(f"squeeze结果: {squeezed}")

def demonstrate_array_concatenation():
    """演示数组合并"""
    print("\n=== 数组合并 ===")
    
    a = np.array([[1, 2], [3, 4]])
    b = np.array([[5, 6], [7, 8]])
    
    print(f"数组a:\n{a}")
    print(f"数组b:\n{b}")
    
    # concatenate - 沿指定轴合并
    concat_axis0 = np.concatenate([a, b], axis=0)  # 垂直合并
    concat_axis1 = np.concatenate([a, b], axis=1)  # 水平合并
    
    print(f"\nconcatenate axis=0 (垂直):\n{concat_axis0}")
    print(f"concatenate axis=1 (水平):\n{concat_axis1}")
    
    # 便捷函数
    vstack_result = np.vstack([a, b])  # 等价于concatenate axis=0
    hstack_result = np.hstack([a, b])  # 等价于concatenate axis=1
    
    print(f"\nvstack:\n{vstack_result}")
    print(f"hstack:\n{hstack_result}")
    
    # stack - 创建新维度
    stack_axis0 = np.stack([a, b], axis=0)
    stack_axis2 = np.stack([a, b], axis=2)
    
    print(f"\nstack axis=0 形状: {stack_axis0.shape}")
    print(f"stack axis=2 形状: {stack_axis2.shape}")
    
    # 多个数组合并
    c = np.array([[9, 10], [11, 12]])
    multi_concat = np.concatenate([a, b, c], axis=0)
    print(f"\n多数组合并:\n{multi_concat}")

def demonstrate_array_splitting():
    """演示数组分割"""
    print("\n=== 数组分割 ===")
    
    arr = np.arange(12).reshape(4, 3)
    print(f"原数组:\n{arr}")
    
    # split - 等分割
    split_result = np.split(arr, 2, axis=0)  # 沿axis=0分成2部分
    print(f"\nsplit axis=0 成2部分:")
    for i, part in enumerate(split_result):
        print(f"部分{i+1}:\n{part}")
    
    # array_split - 不等分割
    unequal_split = np.array_split(arr, 3, axis=0)  # 分成3部分（可能不等）
    print(f"\narray_split axis=0 成3部分:")
    for i, part in enumerate(unequal_split):
        print(f"部分{i+1}:\n{part}")
    
    # hsplit, vsplit
    arr_2d = np.arange(24).reshape(4, 6)
    print(f"\n2D数组:\n{arr_2d}")
    
    hsplit_result = np.hsplit(arr_2d, 3)  # 水平分割成3部分
    print(f"hsplit成3部分:")
    for i, part in enumerate(hsplit_result):
        print(f"部分{i+1}:\n{part}")
    
    vsplit_result = np.vsplit(arr_2d, 2)  # 垂直分割成2部分
    print(f"\nvsplit成2部分:")
    for i, part in enumerate(vsplit_result):
        print(f"部分{i+1}:\n{part}")

def demonstrate_statistical_operations():
    """演示统计和聚合操作"""
    print("\n=== 统计和聚合操作 ===")
    
    # 创建测试数据
    np.random.seed(42)
    data = np.random.randn(4, 5)
    print(f"测试数据:\n{data}")
    
    # 基本统计量
    print(f"\n基本统计量:")
    print(f"均值: {np.mean(data):.4f}")
    print(f"标准差: {np.std(data):.4f}")
    print(f"方差: {np.var(data):.4f}")
    print(f"最大值: {np.max(data):.4f}")
    print(f"最小值: {np.min(data):.4f}")
    print(f"中位数: {np.median(data):.4f}")
    
    # 沿轴统计
    print(f"\n沿轴统计:")
    print(f"沿axis=0的均值 (列均值): {np.mean(data, axis=0)}")
    print(f"沿axis=1的均值 (行均值): {np.mean(data, axis=1)}")
    
    # 累积操作
    print(f"\n累积操作:")
    print(f"累积和: {np.cumsum(data.flatten())[:10]}...")  # 只显示前10个
    print(f"累积乘积: {np.cumprod([1, 2, 3, 4, 5])}")
    
    # 分位数
    print(f"\n分位数:")
    percentiles = [25, 50, 75, 90, 95]
    for p in percentiles:
        value = np.percentile(data, p)
        print(f"{p}%分位数: {value:.4f}")
    
    # 条件统计
    positive_data = data[data > 0]
    print(f"\n条件统计:")
    print(f"正数个数: {np.sum(data > 0)}")
    print(f"正数均值: {np.mean(positive_data):.4f}")
    print(f"绝对值最大的元素: {data.flat[np.argmax(np.abs(data))]:.4f}")

def demonstrate_advanced_indexing():
    """演示高级索引技巧"""
    print("\n=== 高级索引技巧 ===")
    
    # 创建测试数据
    data = np.arange(20).reshape(4, 5)
    print(f"测试数据:\n{data}")
    
    # 布尔索引
    mask = data > 10
    print(f"\n大于10的掩码:\n{mask}")
    print(f"大于10的元素: {data[mask]}")
    
    # 条件替换
    data_copy = data.copy()
    data_copy[data_copy > 15] = -1
    print(f"\n大于15的元素替换为-1:\n{data_copy}")
    
    # 花式索引
    row_indices = [0, 2, 3]
    col_indices = [1, 3, 4]
    selected_elements = data[row_indices, col_indices]
    print(f"\n花式索引选择的元素: {selected_elements}")
    
    # 索引数组
    sorted_indices = np.argsort(data, axis=1)  # 每行排序的索引
    print(f"\n每行排序索引:\n{sorted_indices}")
    
    # 使用索引重排
    sorted_data = np.take_along_axis(data, sorted_indices, axis=1)
    print(f"按索引排序后的数据:\n{sorted_data}")
    
    # where函数
    result = np.where(data > 10, data, 0)  # 大于10保持，否则为0
    print(f"\nwhere条件替换:\n{result}")

def neural_network_data_processing():
    """神经网络数据处理示例"""
    print("\n=== 神经网络数据处理示例 ===")
    
    # 模拟图像数据 (batch_size, height, width, channels)
    batch_size, height, width, channels = 32, 28, 28, 1
    images = np.random.randint(0, 256, (batch_size, height, width, channels))
    
    print(f"原始图像数据形状: {images.shape}")
    
    # 数据预处理
    # 1. 归一化到[0, 1]
    images_normalized = images.astype(np.float32) / 255.0
    
    # 2. 展平为向量 (用于全连接层)
    images_flattened = images_normalized.reshape(batch_size, -1)
    print(f"展平后形状: {images_flattened.shape}")
    
    # 3. 标准化 (零均值，单位方差)
    mean = np.mean(images_flattened, axis=0, keepdims=True)
    std = np.std(images_flattened, axis=0, keepdims=True)
    images_standardized = (images_flattened - mean) / (std + 1e-8)
    
    print(f"标准化后均值: {np.mean(images_standardized):.6f}")
    print(f"标准化后标准差: {np.std(images_standardized):.6f}")
    
    # 4. 数据增强 - 随机翻转
    flip_mask = np.random.random(batch_size) > 0.5
    images_augmented = images_normalized.copy()
    images_augmented[flip_mask] = np.flip(images_augmented[flip_mask], axis=2)
    
    print(f"翻转了 {np.sum(flip_mask)} 张图像")
    
    # 5. 批次分割
    mini_batch_size = 8
    num_mini_batches = batch_size // mini_batch_size
    
    mini_batches = np.array_split(images_standardized, num_mini_batches, axis=0)
    print(f"分割成 {len(mini_batches)} 个小批次")
    for i, batch in enumerate(mini_batches):
        print(f"小批次 {i+1} 形状: {batch.shape}")

# 主程序演示
if __name__ == "__main__":
    print("NumPy数组操作学习")
    print("=" * 50)
    
    demonstrate_shape_manipulation()
    demonstrate_dimension_operations()
    demonstrate_array_concatenation()
    demonstrate_array_splitting()
    demonstrate_statistical_operations()
    demonstrate_advanced_indexing()
    neural_network_data_processing()
    
    print("\n" + "=" * 50)
    print("NumPy数组操作概念总结:")
    print("✓ 形状操作：reshape、flatten、ravel、transpose")
    print("✓ 维度操作：expand_dims、squeeze、newaxis")
    print("✓ 数组合并：concatenate、stack、vstack、hstack")
    print("✓ 数组分割：split、array_split、hsplit、vsplit")
    print("✓ 统计聚合：mean、std、max、min、percentile")
    print("✓ 高级索引：布尔索引、花式索引、条件操作")
    print("✓ 数据处理：归一化、标准化、数据增强")
    print("\n这些操作是数据预处理和神经网络的基础工具！")
