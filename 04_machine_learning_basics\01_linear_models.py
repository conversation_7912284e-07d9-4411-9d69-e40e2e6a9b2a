"""
线性模型 - 机器学习的基础
理解线性回归和逻辑回归，这是理解神经网络的第一步
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Optional

class LinearRegression:
    """线性回归实现"""
    
    def __init__(self, learning_rate: float = 0.01, max_iterations: int = 1000):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.weights = None
        self.bias = None
        self.cost_history = []
    
    def fit(self, X: np.ndarray, y: np.ndarray, method: str = 'gradient_descent'):
        """训练模型
        
        Args:
            X: 特征矩阵 (n_samples, n_features)
            y: 目标值 (n_samples,)
            method: 'gradient_descent' 或 'normal_equation'
        """
        n_samples, n_features = X.shape
        
        if method == 'normal_equation':
            self._fit_normal_equation(X, y)
        else:
            self._fit_gradient_descent(X, y)
    
    def _fit_normal_equation(self, X: np.n<PERSON><PERSON>, y: np.n<PERSON><PERSON>):
        """正规方程解法：θ = (X^T X)^(-1) X^T y"""
        # 添加偏置项
        X_with_bias = np.column_stack([np.ones(X.shape[0]), X])
        
        # 正规方程
        XTX = X_with_bias.T @ X_with_bias
        XTy = X_with_bias.T @ y
        
        # 检查矩阵是否可逆
        try:
            theta = np.linalg.solve(XTX, XTy)
            self.bias = theta[0]
            self.weights = theta[1:]
        except np.linalg.LinAlgError:
            print("矩阵不可逆，使用伪逆")
            theta = np.linalg.pinv(XTX) @ XTy
            self.bias = theta[0]
            self.weights = theta[1:]
    
    def _fit_gradient_descent(self, X: np.ndarray, y: np.ndarray):
        """梯度下降解法"""
        n_samples, n_features = X.shape
        
        # 初始化参数
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0
        self.cost_history = []
        
        for i in range(self.max_iterations):
            # 前向传播
            y_pred = self.predict(X)
            
            # 计算损失
            cost = self._compute_cost(y, y_pred)
            self.cost_history.append(cost)
            
            # 计算梯度
            dw, db = self._compute_gradients(X, y, y_pred)
            
            # 更新参数
            self.weights -= self.learning_rate * dw
            self.bias -= self.learning_rate * db
            
            # 每100次迭代打印一次
            if i % 100 == 0:
                print(f"迭代 {i}, 损失: {cost:.6f}")
    
    def _compute_cost(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算均方误差损失"""
        n_samples = len(y_true)
        cost = np.sum((y_pred - y_true) ** 2) / (2 * n_samples)
        return cost
    
    def _compute_gradients(self, X: np.ndarray, y_true: np.ndarray, y_pred: np.ndarray) -> Tuple[np.ndarray, float]:
        """计算梯度"""
        n_samples = X.shape[0]
        
        # 权重梯度: dw = (1/m) * X^T * (y_pred - y_true)
        dw = (1 / n_samples) * X.T @ (y_pred - y_true)
        
        # 偏置梯度: db = (1/m) * sum(y_pred - y_true)
        db = (1 / n_samples) * np.sum(y_pred - y_true)
        
        return dw, db
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        return X @ self.weights + self.bias
    
    def score(self, X: np.ndarray, y: np.ndarray) -> float:
        """计算R²分数"""
        y_pred = self.predict(X)
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        return r2

class LogisticRegression:
    """逻辑回归实现"""
    
    def __init__(self, learning_rate: float = 0.01, max_iterations: int = 1000):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.weights = None
        self.bias = None
        self.cost_history = []
    
    def sigmoid(self, z: np.ndarray) -> np.ndarray:
        """Sigmoid激活函数"""
        # 数值稳定性处理
        z = np.clip(z, -250, 250)
        return 1 / (1 + np.exp(-z))
    
    def fit(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        n_samples, n_features = X.shape
        
        # 初始化参数
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0
        self.cost_history = []
        
        for i in range(self.max_iterations):
            # 前向传播
            linear_pred = X @ self.weights + self.bias
            y_pred = self.sigmoid(linear_pred)
            
            # 计算损失
            cost = self._compute_cost(y, y_pred)
            self.cost_history.append(cost)
            
            # 计算梯度
            dw, db = self._compute_gradients(X, y, y_pred)
            
            # 更新参数
            self.weights -= self.learning_rate * dw
            self.bias -= self.learning_rate * db
            
            # 每100次迭代打印一次
            if i % 100 == 0:
                print(f"迭代 {i}, 损失: {cost:.6f}")
    
    def _compute_cost(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算交叉熵损失"""
        # 避免log(0)
        epsilon = 1e-15
        y_pred = np.clip(y_pred, epsilon, 1 - epsilon)
        
        n_samples = len(y_true)
        cost = -np.sum(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred)) / n_samples
        return cost
    
    def _compute_gradients(self, X: np.ndarray, y_true: np.ndarray, y_pred: np.ndarray) -> Tuple[np.ndarray, float]:
        """计算梯度"""
        n_samples = X.shape[0]
        
        # 权重梯度
        dw = (1 / n_samples) * X.T @ (y_pred - y_true)
        
        # 偏置梯度
        db = (1 / n_samples) * np.sum(y_pred - y_true)
        
        return dw, db
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """预测概率"""
        linear_pred = X @ self.weights + self.bias
        return self.sigmoid(linear_pred)
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测类别"""
        probabilities = self.predict_proba(X)
        return (probabilities >= 0.5).astype(int)
    
    def score(self, X: np.ndarray, y: np.ndarray) -> float:
        """计算准确率"""
        y_pred = self.predict(X)
        accuracy = np.mean(y_pred == y)
        return accuracy

class PolynomialFeatures:
    """多项式特征生成器"""
    
    def __init__(self, degree: int = 2):
        self.degree = degree
    
    def fit_transform(self, X: np.ndarray) -> np.ndarray:
        """生成多项式特征"""
        if X.ndim == 1:
            X = X.reshape(-1, 1)
        
        n_samples, n_features = X.shape
        
        # 计算所有可能的多项式特征
        features = [np.ones(n_samples)]  # 常数项
        
        for d in range(1, self.degree + 1):
            for i in range(n_features):
                features.append(X[:, i] ** d)
        
        return np.column_stack(features)

def generate_regression_data(n_samples: int = 100, noise: float = 0.1, 
                           random_state: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
    """生成回归数据"""
    if random_state is not None:
        np.random.seed(random_state)
    
    X = np.random.uniform(-2, 2, (n_samples, 1))
    y = 1.5 * X.flatten() + 0.5 + np.random.normal(0, noise, n_samples)
    
    return X, y

def generate_classification_data(n_samples: int = 100, random_state: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
    """生成分类数据"""
    if random_state is not None:
        np.random.seed(random_state)
    
    # 生成两个类别的数据
    n_class0 = n_samples // 2
    n_class1 = n_samples - n_class0
    
    # 类别0：围绕(-1, -1)
    X0 = np.random.multivariate_normal([-1, -1], [[0.5, 0], [0, 0.5]], n_class0)
    y0 = np.zeros(n_class0)
    
    # 类别1：围绕(1, 1)
    X1 = np.random.multivariate_normal([1, 1], [[0.5, 0], [0, 0.5]], n_class1)
    y1 = np.ones(n_class1)
    
    X = np.vstack([X0, X1])
    y = np.hstack([y0, y1])
    
    # 打乱数据
    indices = np.random.permutation(n_samples)
    return X[indices], y[indices]

def demonstrate_linear_regression():
    """演示线性回归"""
    print("=== 线性回归演示 ===")
    
    # 生成数据
    X, y = generate_regression_data(n_samples=100, noise=0.2, random_state=42)
    
    print(f"数据形状: X={X.shape}, y={y.shape}")
    
    # 方法1：梯度下降
    print("\n1. 梯度下降方法:")
    lr_gd = LinearRegression(learning_rate=0.1, max_iterations=500)
    lr_gd.fit(X, y, method='gradient_descent')
    
    print(f"权重: {lr_gd.weights[0]:.4f}")
    print(f"偏置: {lr_gd.bias:.4f}")
    print(f"R²分数: {lr_gd.score(X, y):.4f}")
    
    # 方法2：正规方程
    print("\n2. 正规方程方法:")
    lr_ne = LinearRegression()
    lr_ne.fit(X, y, method='normal_equation')
    
    print(f"权重: {lr_ne.weights[0]:.4f}")
    print(f"偏置: {lr_ne.bias:.4f}")
    print(f"R²分数: {lr_ne.score(X, y):.4f}")
    
    # 比较预测结果
    test_X = np.array([[0], [1], [-1]])
    pred_gd = lr_gd.predict(test_X)
    pred_ne = lr_ne.predict(test_X)
    
    print(f"\n预测比较:")
    for i, x in enumerate(test_X.flatten()):
        print(f"X={x}: 梯度下降={pred_gd[i]:.4f}, 正规方程={pred_ne[i]:.4f}")

def demonstrate_logistic_regression():
    """演示逻辑回归"""
    print("\n=== 逻辑回归演示 ===")
    
    # 生成数据
    X, y = generate_classification_data(n_samples=200, random_state=42)
    
    print(f"数据形状: X={X.shape}, y={y.shape}")
    print(f"类别分布: {np.bincount(y)}")
    
    # 训练模型
    lr = LogisticRegression(learning_rate=0.1, max_iterations=1000)
    lr.fit(X, y)
    
    print(f"权重: {lr.weights}")
    print(f"偏置: {lr.bias:.4f}")
    print(f"准确率: {lr.score(X, y):.4f}")
    
    # 预测示例
    test_points = np.array([[-1, -1], [1, 1], [0, 0]])
    probabilities = lr.predict_proba(test_points)
    predictions = lr.predict(test_points)
    
    print(f"\n预测示例:")
    for i, point in enumerate(test_points):
        print(f"点{point}: 概率={probabilities[i]:.4f}, 预测={predictions[i]}")

def demonstrate_polynomial_regression():
    """演示多项式回归"""
    print("\n=== 多项式回归演示 ===")
    
    # 生成非线性数据
    np.random.seed(42)
    X = np.linspace(-2, 2, 50).reshape(-1, 1)
    y = 0.5 * X.flatten()**3 - X.flatten()**2 + 0.5 * X.flatten() + np.random.normal(0, 0.3, 50)
    
    print(f"原始数据形状: X={X.shape}, y={y.shape}")
    
    # 不同阶数的多项式拟合
    degrees = [1, 2, 3, 5]
    
    for degree in degrees:
        print(f"\n{degree}阶多项式:")
        
        # 生成多项式特征
        poly = PolynomialFeatures(degree=degree)
        X_poly = poly.fit_transform(X)
        
        print(f"多项式特征形状: {X_poly.shape}")
        
        # 训练模型
        lr = LinearRegression()
        lr.fit(X_poly, y, method='normal_equation')
        
        # 评估
        r2_score = lr.score(X_poly, y)
        print(f"R²分数: {r2_score:.4f}")
        
        # 检查过拟合
        if degree > 3:
            print("⚠️ 高阶多项式可能存在过拟合风险")

def compare_optimization_methods():
    """比较不同优化方法"""
    print("\n=== 优化方法比较 ===")
    
    # 生成较大的数据集
    np.random.seed(42)
    n_samples, n_features = 1000, 10
    X = np.random.randn(n_samples, n_features)
    true_weights = np.random.randn(n_features)
    y = X @ true_weights + np.random.normal(0, 0.1, n_samples)
    
    print(f"数据集大小: {n_samples} 样本, {n_features} 特征")
    
    # 梯度下降
    import time
    
    start_time = time.time()
    lr_gd = LinearRegression(learning_rate=0.01, max_iterations=1000)
    lr_gd.fit(X, y, method='gradient_descent')
    gd_time = time.time() - start_time
    
    # 正规方程
    start_time = time.time()
    lr_ne = LinearRegression()
    lr_ne.fit(X, y, method='normal_equation')
    ne_time = time.time() - start_time
    
    print(f"\n性能比较:")
    print(f"梯度下降时间: {gd_time:.4f}秒")
    print(f"正规方程时间: {ne_time:.4f}秒")
    
    print(f"\n精度比较:")
    print(f"梯度下降R²: {lr_gd.score(X, y):.6f}")
    print(f"正规方程R²: {lr_ne.score(X, y):.6f}")
    
    # 参数比较
    weight_diff = np.linalg.norm(lr_gd.weights - lr_ne.weights)
    bias_diff = abs(lr_gd.bias - lr_ne.bias)
    
    print(f"\n参数差异:")
    print(f"权重差异: {weight_diff:.6f}")
    print(f"偏置差异: {bias_diff:.6f}")

# 主程序演示
if __name__ == "__main__":
    print("线性模型学习")
    print("=" * 50)
    
    demonstrate_linear_regression()
    demonstrate_logistic_regression()
    demonstrate_polynomial_regression()
    compare_optimization_methods()
    
    print("\n" + "=" * 50)
    print("线性模型概念总结:")
    print("✓ 线性回归：连续值预测，最小二乘法")
    print("✓ 逻辑回归：分类问题，Sigmoid函数")
    print("✓ 梯度下降：迭代优化算法")
    print("✓ 正规方程：解析解，适用于小数据集")
    print("✓ 多项式回归：非线性关系建模")
    print("✓ 损失函数：MSE（回归）、交叉熵（分类）")
    print("✓ 评估指标：R²分数、准确率")
    print("\n这些是理解神经网络的重要基础！")
