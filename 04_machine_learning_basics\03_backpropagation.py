"""
反向传播算法详解 - 神经网络训练的核心
深入理解反向传播的数学原理和实现细节
"""

import numpy as np
from typing import List, Tuple, Dict
import time

class BackpropagationDemo:
    """反向传播算法演示类"""
    
    def __init__(self, layer_sizes: List[int], learning_rate: float = 0.01):
        """
        初始化网络
        
        Args:
            layer_sizes: 每层神经元数量，如[2, 3, 1]
            learning_rate: 学习率
        """
        self.layer_sizes = layer_sizes
        self.num_layers = len(layer_sizes)
        self.learning_rate = learning_rate
        
        # 初始化权重和偏置
        self.weights = []
        self.biases = []
        
        for i in range(self.num_layers - 1):
            # 小随机数初始化
            w = np.random.randn(layer_sizes[i], layer_sizes[i+1]) * 0.5
            b = np.zeros((1, layer_sizes[i+1]))
            
            self.weights.append(w)
            self.biases.append(b)
        
        # 用于存储中间计算结果
        self.z_values = []  # 线性输出
        self.activations = []  # 激活值
        self.deltas = []  # 误差项
    
    def sigmoid(self, x: np.ndarray) -> np.ndarray:
        """Sigmoid激活函数"""
        x = np.clip(x, -500, 500)
        return 1 / (1 + np.exp(-x))
    
    def sigmoid_derivative(self, x: np.ndarray) -> np.ndarray:
        """Sigmoid导数"""
        s = self.sigmoid(x)
        return s * (1 - s)
    
    def forward_propagation_detailed(self, X: np.ndarray, verbose: bool = False) -> np.ndarray:
        """详细的前向传播过程"""
        if verbose:
            print("=== 前向传播过程 ===")
        
        self.z_values = []
        self.activations = [X]
        
        current_input = X
        
        for i in range(self.num_layers - 1):
            if verbose:
                print(f"\n第 {i+1} 层:")
                print(f"  输入形状: {current_input.shape}")
                print(f"  权重形状: {self.weights[i].shape}")
                print(f"  偏置形状: {self.biases[i].shape}")
            
            # 线性变换: z = X @ W + b
            z = current_input @ self.weights[i] + self.biases[i]
            self.z_values.append(z)
            
            if verbose:
                print(f"  线性输出 z: {z.flatten()}")
            
            # 激活函数
            a = self.sigmoid(z)
            self.activations.append(a)
            
            if verbose:
                print(f"  激活输出 a: {a.flatten()}")
            
            current_input = a
        
        return self.activations[-1]
    
    def backward_propagation_detailed(self, X: np.ndarray, y: np.ndarray, verbose: bool = False) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """详细的反向传播过程"""
        if verbose:
            print("\n=== 反向传播过程 ===")
        
        m = X.shape[0]  # 样本数量
        
        # 初始化梯度
        weight_gradients = []
        bias_gradients = []
        self.deltas = []
        
        # 输出层误差
        output_error = self.activations[-1] - y.reshape(-1, 1)
        delta = output_error
        self.deltas.append(delta)
        
        if verbose:
            print(f"\n输出层误差:")
            print(f"  预测值: {self.activations[-1].flatten()}")
            print(f"  真实值: {y}")
            print(f"  误差: {output_error.flatten()}")
        
        # 从输出层向输入层反向传播
        for i in range(self.num_layers - 2, -1, -1):
            if verbose:
                print(f"\n第 {i+1} 层反向传播:")
            
            # 计算权重梯度: dW = a^(l-1) @ delta^(l)
            dW = self.activations[i].T @ delta / m
            weight_gradients.insert(0, dW)
            
            # 计算偏置梯度: db = mean(delta^(l))
            db = np.mean(delta, axis=0, keepdims=True)
            bias_gradients.insert(0, db)
            
            if verbose:
                print(f"  权重梯度形状: {dW.shape}")
                print(f"  权重梯度: \n{dW}")
                print(f"  偏置梯度: {db.flatten()}")
            
            # 计算前一层的误差（如果不是输入层）
            if i > 0:
                # delta^(l-1) = (delta^(l) @ W^(l).T) * sigmoid'(z^(l-1))
                delta = (delta @ self.weights[i].T) * self.sigmoid_derivative(self.z_values[i-1])
                self.deltas.insert(0, delta)
                
                if verbose:
                    print(f"  传播到前一层的误差: {delta.flatten()}")
        
        return weight_gradients, bias_gradients
    
    def update_parameters(self, weight_gradients: List[np.ndarray], bias_gradients: List[np.ndarray], verbose: bool = False):
        """更新参数"""
        if verbose:
            print("\n=== 参数更新 ===")
        
        for i in range(len(self.weights)):
            old_weights = self.weights[i].copy()
            old_biases = self.biases[i].copy()
            
            # 梯度下降更新
            self.weights[i] -= self.learning_rate * weight_gradients[i]
            self.biases[i] -= self.learning_rate * bias_gradients[i]
            
            if verbose:
                print(f"\n第 {i+1} 层参数更新:")
                print(f"  权重变化: {np.mean(np.abs(self.weights[i] - old_weights)):.6f}")
                print(f"  偏置变化: {np.mean(np.abs(self.biases[i] - old_biases)):.6f}")
    
    def compute_loss(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算均方误差损失"""
        return np.mean((y_pred - y_true.reshape(-1, 1)) ** 2) / 2
    
    def train_step(self, X: np.ndarray, y: np.ndarray, verbose: bool = False) -> float:
        """单步训练"""
        # 前向传播
        y_pred = self.forward_propagation_detailed(X, verbose)
        
        # 计算损失
        loss = self.compute_loss(y, y_pred)
        
        # 反向传播
        weight_grads, bias_grads = self.backward_propagation_detailed(X, y, verbose)
        
        # 更新参数
        self.update_parameters(weight_grads, bias_grads, verbose)
        
        return loss

class GradientChecker:
    """梯度检查器 - 验证反向传播的正确性"""
    
    def __init__(self, network: BackpropagationDemo):
        self.network = network
    
    def numerical_gradient(self, X: np.ndarray, y: np.ndarray, epsilon: float = 1e-7) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """数值方法计算梯度"""
        numerical_weight_grads = []
        numerical_bias_grads = []
        
        # 检查权重梯度
        for layer_idx in range(len(self.network.weights)):
            weight_grad = np.zeros_like(self.network.weights[layer_idx])
            
            for i in range(self.network.weights[layer_idx].shape[0]):
                for j in range(self.network.weights[layer_idx].shape[1]):
                    # w + epsilon
                    self.network.weights[layer_idx][i, j] += epsilon
                    y_pred_plus = self.network.forward_propagation_detailed(X)
                    loss_plus = self.network.compute_loss(y, y_pred_plus)
                    
                    # w - epsilon
                    self.network.weights[layer_idx][i, j] -= 2 * epsilon
                    y_pred_minus = self.network.forward_propagation_detailed(X)
                    loss_minus = self.network.compute_loss(y, y_pred_minus)
                    
                    # 恢复原值
                    self.network.weights[layer_idx][i, j] += epsilon
                    
                    # 数值梯度
                    weight_grad[i, j] = (loss_plus - loss_minus) / (2 * epsilon)
            
            numerical_weight_grads.append(weight_grad)
        
        # 检查偏置梯度
        for layer_idx in range(len(self.network.biases)):
            bias_grad = np.zeros_like(self.network.biases[layer_idx])
            
            for j in range(self.network.biases[layer_idx].shape[1]):
                # b + epsilon
                self.network.biases[layer_idx][0, j] += epsilon
                y_pred_plus = self.network.forward_propagation_detailed(X)
                loss_plus = self.network.compute_loss(y, y_pred_plus)
                
                # b - epsilon
                self.network.biases[layer_idx][0, j] -= 2 * epsilon
                y_pred_minus = self.network.forward_propagation_detailed(X)
                loss_minus = self.network.compute_loss(y, y_pred_minus)
                
                # 恢复原值
                self.network.biases[layer_idx][0, j] += epsilon
                
                # 数值梯度
                bias_grad[0, j] = (loss_plus - loss_minus) / (2 * epsilon)
            
            numerical_bias_grads.append(bias_grad)
        
        return numerical_weight_grads, numerical_bias_grads
    
    def check_gradients(self, X: np.ndarray, y: np.ndarray) -> bool:
        """检查梯度计算的正确性"""
        print("=== 梯度检查 ===")
        
        # 前向传播
        self.network.forward_propagation_detailed(X)
        
        # 解析梯度
        analytical_weight_grads, analytical_bias_grads = self.network.backward_propagation_detailed(X, y)
        
        # 数值梯度
        numerical_weight_grads, numerical_bias_grads = self.numerical_gradient(X, y)
        
        # 比较梯度
        all_correct = True
        
        for i in range(len(analytical_weight_grads)):
            weight_diff = np.abs(analytical_weight_grads[i] - numerical_weight_grads[i])
            bias_diff = np.abs(analytical_bias_grads[i] - numerical_bias_grads[i])
            
            max_weight_diff = np.max(weight_diff)
            max_bias_diff = np.max(bias_diff)
            
            print(f"\n第 {i+1} 层:")
            print(f"  权重梯度最大差异: {max_weight_diff:.8f}")
            print(f"  偏置梯度最大差异: {max_bias_diff:.8f}")
            
            if max_weight_diff > 1e-5 or max_bias_diff > 1e-5:
                print(f"  ⚠️ 梯度差异过大！")
                all_correct = False
            else:
                print(f"  ✓ 梯度检查通过")
        
        return all_correct

def demonstrate_simple_example():
    """演示简单的反向传播例子"""
    print("=== 简单反向传播演示 ===")
    
    # 创建简单网络：2输入 -> 2隐藏 -> 1输出
    network = BackpropagationDemo([2, 2, 1], learning_rate=0.5)
    
    # 简单训练数据
    X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])
    y = np.array([0, 1, 1, 0])  # XOR问题
    
    print(f"训练数据:")
    print(f"X = \n{X}")
    print(f"y = {y}")
    
    # 演示单步训练过程
    print(f"\n初始权重和偏置:")
    for i, (w, b) in enumerate(zip(network.weights, network.biases)):
        print(f"第 {i+1} 层权重:\n{w}")
        print(f"第 {i+1} 层偏置: {b.flatten()}")
    
    # 使用第一个样本进行详细演示
    sample_X = X[0:1]  # 取第一个样本
    sample_y = y[0:1]
    
    print(f"\n使用样本: X={sample_X.flatten()}, y={sample_y}")
    
    loss = network.train_step(sample_X, sample_y, verbose=True)
    print(f"\n训练后损失: {loss:.6f}")

def demonstrate_gradient_checking():
    """演示梯度检查"""
    print("\n=== 梯度检查演示 ===")
    
    # 创建小网络
    network = BackpropagationDemo([2, 3, 1], learning_rate=0.1)
    
    # 测试数据
    X = np.array([[1, 2]])
    y = np.array([0.5])
    
    # 梯度检查
    checker = GradientChecker(network)
    is_correct = checker.check_gradients(X, y)
    
    if is_correct:
        print("\n✓ 反向传播实现正确！")
    else:
        print("\n✗ 反向传播实现有误！")

def demonstrate_learning_process():
    """演示学习过程"""
    print("\n=== 学习过程演示 ===")
    
    # 创建网络
    network = BackpropagationDemo([2, 4, 1], learning_rate=0.5)
    
    # XOR数据
    X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])
    y = np.array([0, 1, 1, 0])
    
    print("训练XOR问题...")
    
    losses = []
    epochs = 1000
    
    for epoch in range(epochs):
        epoch_loss = 0
        
        # 对每个样本进行训练
        for i in range(len(X)):
            sample_X = X[i:i+1]
            sample_y = y[i:i+1]
            
            loss = network.train_step(sample_X, sample_y, verbose=False)
            epoch_loss += loss
        
        avg_loss = epoch_loss / len(X)
        losses.append(avg_loss)
        
        if epoch % 100 == 0:
            print(f"Epoch {epoch}, 平均损失: {avg_loss:.6f}")
    
    # 测试最终结果
    print(f"\n最终测试结果:")
    for i in range(len(X)):
        sample_X = X[i:i+1]
        prediction = network.forward_propagation_detailed(sample_X)
        print(f"输入: {X[i]}, 期望: {y[i]}, 预测: {prediction[0][0]:.4f}")

def compare_learning_rates():
    """比较不同学习率的效果"""
    print("\n=== 学习率比较 ===")
    
    learning_rates = [0.1, 0.5, 1.0, 2.0]
    
    # XOR数据
    X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])
    y = np.array([0, 1, 1, 0])
    
    for lr in learning_rates:
        print(f"\n学习率: {lr}")
        
        network = BackpropagationDemo([2, 4, 1], learning_rate=lr)
        
        # 训练
        final_losses = []
        epochs = 500
        
        for epoch in range(epochs):
            epoch_loss = 0
            
            for i in range(len(X)):
                sample_X = X[i:i+1]
                sample_y = y[i:i+1]
                
                loss = network.train_step(sample_X, sample_y, verbose=False)
                epoch_loss += loss
            
            if epoch == epochs - 1:
                final_losses.append(epoch_loss / len(X))
        
        print(f"最终损失: {final_losses[0]:.6f}")
        
        # 测试准确性
        correct = 0
        for i in range(len(X)):
            sample_X = X[i:i+1]
            prediction = network.forward_propagation_detailed(sample_X)
            predicted_class = 1 if prediction[0][0] > 0.5 else 0
            if predicted_class == y[i]:
                correct += 1
        
        accuracy = correct / len(X)
        print(f"准确率: {accuracy:.2f}")
        
        if lr > 1.5:
            print("⚠️ 学习率过大可能导致训练不稳定")

# 主程序演示
if __name__ == "__main__":
    print("反向传播算法详解")
    print("=" * 50)
    
    demonstrate_simple_example()
    demonstrate_gradient_checking()
    demonstrate_learning_process()
    compare_learning_rates()
    
    print("\n" + "=" * 50)
    print("反向传播算法总结:")
    print("✓ 前向传播：计算网络输出和中间值")
    print("✓ 损失计算：衡量预测与真实值的差异")
    print("✓ 反向传播：利用链式法则计算梯度")
    print("✓ 参数更新：使用梯度下降更新权重和偏置")
    print("✓ 梯度检查：验证反向传播实现的正确性")
    print("✓ 学习率：控制参数更新的步长")
    print("\n反向传播是训练神经网络的核心算法！")
