"""
概率论和统计基础 - 机器学习的理论基础
重点理解概率分布、期望、方差，这些是损失函数和不确定性建模的基础
"""

import math
import random
from typing import List, Tuple, Callable
from collections import Counter

# 1. 基础概率概念
class BasicProbability:
    """基础概率计算"""
    
    @staticmethod
    def classical_probability(favorable_outcomes: int, total_outcomes: int) -> float:
        """古典概率：P(A) = 有利结果数 / 总结果数"""
        if total_outcomes == 0:
            raise ValueError("总结果数不能为0")
        return favorable_outcomes / total_outcomes
    
    @staticmethod
    def conditional_probability(p_ab: float, p_b: float) -> float:
        """条件概率：P(A|B) = P(A∩B) / P(B)"""
        if p_b == 0:
            raise ValueError("P(B)不能为0")
        return p_ab / p_b
    
    @staticmethod
    def bayes_theorem(p_b_given_a: float, p_a: float, p_b: float) -> float:
        """贝叶斯定理：P(A|B) = P(B|A) * P(A) / P(B)"""
        if p_b == 0:
            raise ValueError("P(B)不能为0")
        return (p_b_given_a * p_a) / p_b
    
    @staticmethod
    def independence_test(p_a: float, p_b: float, p_ab: float, tolerance: float = 1e-6) -> bool:
        """检验两事件是否独立：P(A∩B) = P(A) * P(B)"""
        return abs(p_ab - p_a * p_b) < tolerance

# 2. 随机变量和分布
class RandomVariable:
    """随机变量基类"""
    
    def __init__(self, name: str):
        self.name = name
    
    def expectation(self) -> float:
        """期望值"""
        raise NotImplementedError
    
    def variance(self) -> float:
        """方差"""
        raise NotImplementedError
    
    def standard_deviation(self) -> float:
        """标准差"""
        return math.sqrt(self.variance())

class DiscreteUniform(RandomVariable):
    """离散均匀分布"""
    
    def __init__(self, values: List[float]):
        super().__init__("DiscreteUniform")
        self.values = values
        self.n = len(values)
    
    def probability(self, x: float) -> float:
        """概率质量函数"""
        return 1/self.n if x in self.values else 0
    
    def expectation(self) -> float:
        """期望：E[X] = (1/n) * Σx_i"""
        return sum(self.values) / self.n
    
    def variance(self) -> float:
        """方差：Var[X] = E[X²] - (E[X])²"""
        mean = self.expectation()
        return sum((x - mean)**2 for x in self.values) / self.n

class Bernoulli(RandomVariable):
    """伯努利分布 - 二分类问题的基础"""
    
    def __init__(self, p: float):
        super().__init__("Bernoulli")
        if not 0 <= p <= 1:
            raise ValueError("概率p必须在[0,1]之间")
        self.p = p
    
    def probability(self, x: int) -> float:
        """概率质量函数"""
        if x == 1:
            return self.p
        elif x == 0:
            return 1 - self.p
        else:
            return 0
    
    def expectation(self) -> float:
        """期望：E[X] = p"""
        return self.p
    
    def variance(self) -> float:
        """方差：Var[X] = p(1-p)"""
        return self.p * (1 - self.p)

class Normal(RandomVariable):
    """正态分布 - 最重要的连续分布"""
    
    def __init__(self, mu: float, sigma: float):
        super().__init__("Normal")
        if sigma <= 0:
            raise ValueError("标准差必须大于0")
        self.mu = mu      # 均值
        self.sigma = sigma # 标准差
    
    def pdf(self, x: float) -> float:
        """概率密度函数"""
        coefficient = 1 / (self.sigma * math.sqrt(2 * math.pi))
        exponent = -0.5 * ((x - self.mu) / self.sigma) ** 2
        return coefficient * math.exp(exponent)
    
    def expectation(self) -> float:
        """期望：E[X] = μ"""
        return self.mu
    
    def variance(self) -> float:
        """方差：Var[X] = σ²"""
        return self.sigma ** 2
    
    def sample(self, n: int = 1) -> List[float]:
        """生成样本（Box-Muller变换）"""
        samples = []
        for _ in range(n):
            # Box-Muller变换生成正态分布样本
            u1 = random.random()
            u2 = random.random()
            z = math.sqrt(-2 * math.log(u1)) * math.cos(2 * math.pi * u2)
            x = self.mu + self.sigma * z
            samples.append(x)
        return samples

# 3. 统计量计算
class Statistics:
    """统计量计算工具"""
    
    @staticmethod
    def mean(data: List[float]) -> float:
        """样本均值"""
        return sum(data) / len(data)
    
    @staticmethod
    def variance(data: List[float], ddof: int = 1) -> float:
        """样本方差
        ddof=1: 无偏估计 (除以n-1)
        ddof=0: 有偏估计 (除以n)
        """
        mean_val = Statistics.mean(data)
        n = len(data)
        return sum((x - mean_val)**2 for x in data) / (n - ddof)
    
    @staticmethod
    def standard_deviation(data: List[float], ddof: int = 1) -> float:
        """样本标准差"""
        return math.sqrt(Statistics.variance(data, ddof))
    
    @staticmethod
    def covariance(x: List[float], y: List[float]) -> float:
        """协方差"""
        if len(x) != len(y):
            raise ValueError("两个变量的样本数必须相同")
        
        mean_x = Statistics.mean(x)
        mean_y = Statistics.mean(y)
        n = len(x)
        
        return sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n)) / (n - 1)
    
    @staticmethod
    def correlation(x: List[float], y: List[float]) -> float:
        """皮尔逊相关系数"""
        cov_xy = Statistics.covariance(x, y)
        std_x = Statistics.standard_deviation(x)
        std_y = Statistics.standard_deviation(y)
        
        if std_x == 0 or std_y == 0:
            return 0
        
        return cov_xy / (std_x * std_y)

# 4. 信息论基础
class InformationTheory:
    """信息论概念 - 机器学习中的重要工具"""
    
    @staticmethod
    def entropy(probabilities: List[float]) -> float:
        """信息熵：H(X) = -Σ p(x) log₂ p(x)"""
        entropy = 0
        for p in probabilities:
            if p > 0:  # 避免log(0)
                entropy -= p * math.log2(p)
        return entropy
    
    @staticmethod
    def cross_entropy(true_probs: List[float], pred_probs: List[float]) -> float:
        """交叉熵：H(p,q) = -Σ p(x) log q(x)"""
        if len(true_probs) != len(pred_probs):
            raise ValueError("概率分布长度必须相同")
        
        cross_entropy = 0
        for p, q in zip(true_probs, pred_probs):
            if p > 0 and q > 0:
                cross_entropy -= p * math.log(q)
        return cross_entropy
    
    @staticmethod
    def kl_divergence(p: List[float], q: List[float]) -> float:
        """KL散度：D_KL(P||Q) = Σ p(x) log(p(x)/q(x))"""
        if len(p) != len(q):
            raise ValueError("概率分布长度必须相同")
        
        kl_div = 0
        for p_i, q_i in zip(p, q):
            if p_i > 0 and q_i > 0:
                kl_div += p_i * math.log(p_i / q_i)
        return kl_div

# 5. 机器学习中的概率应用
class MLProbabilityApplications:
    """机器学习中的概率应用"""
    
    @staticmethod
    def softmax(logits: List[float]) -> List[float]:
        """Softmax函数 - 将logits转换为概率分布"""
        # 数值稳定性：减去最大值
        max_logit = max(logits)
        exp_logits = [math.exp(x - max_logit) for x in logits]
        sum_exp = sum(exp_logits)
        return [x / sum_exp for x in exp_logits]
    
    @staticmethod
    def log_likelihood(data: List[float], distribution: Normal) -> float:
        """对数似然函数"""
        log_likelihood = 0
        for x in data:
            pdf_value = distribution.pdf(x)
            if pdf_value > 0:
                log_likelihood += math.log(pdf_value)
        return log_likelihood
    
    @staticmethod
    def maximum_likelihood_normal(data: List[float]) -> Tuple[float, float]:
        """正态分布的最大似然估计"""
        mu_mle = Statistics.mean(data)
        sigma_mle = math.sqrt(Statistics.variance(data, ddof=0))
        return mu_mle, sigma_mle

# 演示使用
if __name__ == "__main__":
    print("=== 概率论和统计基础演示 ===")
    
    # 1. 基础概率
    print("1. 基础概率计算:")
    
    # 掷骰子例子
    favorable = 2  # 掷出1或2
    total = 6
    prob = BasicProbability.classical_probability(favorable, total)
    print(f"掷骰子得到1或2的概率: {prob:.3f}")
    
    # 贝叶斯定理例子
    p_disease = 0.01  # 疾病患病率
    p_positive_given_disease = 0.95  # 有病时检测为阳性的概率
    p_positive = 0.05  # 检测为阳性的总概率
    
    p_disease_given_positive = BasicProbability.bayes_theorem(
        p_positive_given_disease, p_disease, p_positive
    )
    print(f"检测阳性时真正患病的概率: {p_disease_given_positive:.3f}")
    
    # 2. 随机变量和分布
    print("\n2. 随机变量和分布:")
    
    # 伯努利分布（模拟硬币）
    coin = Bernoulli(0.6)  # 不公平硬币
    print(f"伯努利分布 p=0.6:")
    print(f"  期望: {coin.expectation():.3f}")
    print(f"  方差: {coin.variance():.3f}")
    print(f"  P(X=1): {coin.probability(1):.3f}")
    
    # 正态分布
    normal = Normal(mu=0, sigma=1)  # 标准正态分布
    print(f"\n标准正态分布:")
    print(f"  期望: {normal.expectation():.3f}")
    print(f"  方差: {normal.variance():.3f}")
    print(f"  PDF(0): {normal.pdf(0):.3f}")
    
    # 生成样本
    samples = normal.sample(1000)
    print(f"  1000个样本的均值: {Statistics.mean(samples):.3f}")
    print(f"  1000个样本的标准差: {Statistics.standard_deviation(samples):.3f}")
    
    # 3. 统计量计算
    print("\n3. 统计量计算:")
    
    # 生成两组相关数据
    x_data = [i + random.gauss(0, 0.5) for i in range(20)]
    y_data = [2*x + 1 + random.gauss(0, 1) for x in x_data]
    
    correlation = Statistics.correlation(x_data, y_data)
    print(f"两组数据的相关系数: {correlation:.3f}")
    
    # 4. 信息论
    print("\n4. 信息论:")
    
    # 计算熵
    fair_coin = [0.5, 0.5]
    biased_coin = [0.9, 0.1]
    
    entropy_fair = InformationTheory.entropy(fair_coin)
    entropy_biased = InformationTheory.entropy(biased_coin)
    
    print(f"公平硬币的熵: {entropy_fair:.3f} bits")
    print(f"偏向硬币的熵: {entropy_biased:.3f} bits")
    
    # 交叉熵（分类损失函数）
    true_dist = [1, 0, 0]  # 真实标签：第一类
    pred_dist1 = [0.8, 0.15, 0.05]  # 好的预测
    pred_dist2 = [0.3, 0.4, 0.3]    # 差的预测
    
    ce1 = InformationTheory.cross_entropy(true_dist, pred_dist1)
    ce2 = InformationTheory.cross_entropy(true_dist, pred_dist2)
    
    print(f"好预测的交叉熵: {ce1:.3f}")
    print(f"差预测的交叉熵: {ce2:.3f}")
    
    # 5. Softmax演示
    print("\n5. Softmax函数:")
    
    logits = [2.0, 1.0, 0.1]
    probabilities = MLProbabilityApplications.softmax(logits)
    
    print(f"Logits: {logits}")
    print(f"Softmax概率: {[f'{p:.3f}' for p in probabilities]}")
    print(f"概率和: {sum(probabilities):.6f}")
    
    # 6. 最大似然估计
    print("\n6. 最大似然估计:")
    
    # 生成已知参数的正态分布数据
    true_mu, true_sigma = 5.0, 2.0
    true_normal = Normal(true_mu, true_sigma)
    data = true_normal.sample(100)
    
    # 估计参数
    estimated_mu, estimated_sigma = MLProbabilityApplications.maximum_likelihood_normal(data)
    
    print(f"真实参数: μ={true_mu}, σ={true_sigma}")
    print(f"估计参数: μ={estimated_mu:.3f}, σ={estimated_sigma:.3f}")
    
    print("\n=== 关键概念总结 ===")
    print("✓ 概率：不确定性的数学描述")
    print("✓ 条件概率：给定条件下的概率")
    print("✓ 贝叶斯定理：更新先验概率")
    print("✓ 随机变量：概率现象的数学模型")
    print("✓ 期望和方差：分布的重要特征")
    print("✓ 信息熵：不确定性的度量")
    print("✓ 交叉熵：分类问题的损失函数")
    print("✓ 最大似然估计：参数估计方法")
