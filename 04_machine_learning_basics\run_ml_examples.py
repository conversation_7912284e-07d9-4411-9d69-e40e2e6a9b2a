"""
运行所有机器学习基础示例
这个脚本会依次运行所有的机器学习基础示例，帮助你掌握核心算法
"""

import sys
import os
import importlib.util

def run_module(module_path, module_name):
    """动态运行模块"""
    print(f"\n{'='*60}")
    print(f"运行 {module_name}")
    print(f"{'='*60}")
    
    try:
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print(f"\n✓ {module_name} 运行成功!")
        
    except Exception as e:
        print(f"\n✗ {module_name} 运行失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("机器学习基础 - 示例代码运行器")
    print("这将帮助你逐步掌握机器学习的核心算法")
    
    # 要运行的模块列表
    modules = [
        ("01_linear_models.py", "线性模型"),
        ("02_neural_networks.py", "神经网络基础"),
        ("03_backpropagation.py", "反向传播算法"),
        ("04_optimization_algorithms.py", "优化算法"),
        ("ml_practice_project.py", "机器学习综合实践项目")
    ]
    
    success_count = 0
    total_count = len(modules)
    
    for module_file, module_desc in modules:
        module_path = os.path.join(os.path.dirname(__file__), module_file)
        
        if os.path.exists(module_path):
            if run_module(module_path, module_desc):
                success_count += 1
        else:
            print(f"\n✗ 文件不存在: {module_file}")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"运行总结")
    print(f"{'='*60}")
    print(f"成功运行: {success_count}/{total_count} 个模块")
    
    if success_count == total_count:
        print("\n🎉 恭喜！所有机器学习基础示例都运行成功！")
        print("你已经掌握了机器学习的核心算法，可以进入下一阶段的学习了。")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个模块运行失败，请检查代码。")
    
    print("\n机器学习核心概念掌握检查清单:")
    print("□ 线性回归和逻辑回归")
    print("□ 梯度下降算法")
    print("□ 感知机和多层感知机")
    print("□ 激活函数的作用和选择")
    print("□ 前向传播和反向传播")
    print("□ 链式法则在反向传播中的应用")
    print("□ 不同优化算法的特点")
    print("□ 学习率对训练的影响")
    print("□ 正则化技术防止过拟合")
    print("□ 模型评估指标")
    
    print("\n算法理解程度检验:")
    print("• 能否手工计算简单神经网络的前向传播？")
    print("• 能否推导反向传播的梯度计算公式？")
    print("• 理解不同优化算法的优缺点？")
    print("• 知道如何选择合适的激活函数？")
    print("• 理解过拟合和欠拟合的概念？")
    
    print("\n与深度学习的联系:")
    print("• 线性模型 → 神经网络层")
    print("• 多层感知机 → 深度神经网络")
    print("• 反向传播 → 深度网络训练")
    print("• 优化算法 → 深度学习优化")
    print("• 正则化 → 深度学习正则化技术")
    print("• 激活函数 → 深度网络的非线性")
    
    print("\n下一步学习建议:")
    print("1. 确保理解每个算法的数学原理")
    print("2. 练习手工推导梯度计算")
    print("3. 实验不同超参数的影响")
    print("4. 理解各种技术在深度学习中的应用")
    print("5. 准备进入深度学习框架（PyTorch）学习阶段")

if __name__ == "__main__":
    main()
