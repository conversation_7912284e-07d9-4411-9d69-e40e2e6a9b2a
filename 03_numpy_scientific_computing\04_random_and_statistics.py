"""
NumPy随机数和统计 - 随机数生成、概率分布、统计分析
这些功能在机器学习中用于数据生成、参数初始化、采样等
"""

import numpy as np
import matplotlib.pyplot as plt

def demonstrate_random_number_generation():
    """演示随机数生成"""
    print("=== 随机数生成 ===")
    
    # 设置随机种子
    np.random.seed(42)
    print("设置随机种子为42，确保结果可重现")
    
    # 基本随机数
    print(f"\n基本随机数:")
    print(f"单个随机数 [0,1): {np.random.random():.4f}")
    print(f"随机数组 (3,): {np.random.random(3)}")
    print(f"随机矩阵 (2,3):\n{np.random.random((2, 3))}")
    
    # 整数随机数
    print(f"\n整数随机数:")
    print(f"随机整数 [0,10): {np.random.randint(0, 10)}")
    print(f"随机整数数组: {np.random.randint(0, 10, size=5)}")
    print(f"随机整数矩阵:\n{np.random.randint(0, 100, size=(3, 4))}")
    
    # 从数组中随机选择
    choices = ['A', 'B', 'C', 'D', 'E']
    print(f"\n随机选择:")
    print(f"从{choices}中随机选择: {np.random.choice(choices)}")
    print(f"有放回抽样3个: {np.random.choice(choices, size=3)}")
    print(f"无放回抽样3个: {np.random.choice(choices, size=3, replace=False)}")
    
    # 带权重的随机选择
    weights = [0.1, 0.2, 0.3, 0.3, 0.1]
    weighted_choice = np.random.choice(choices, size=10, p=weights)
    print(f"带权重的选择: {weighted_choice}")

def demonstrate_probability_distributions():
    """演示概率分布"""
    print("\n=== 概率分布 ===")
    
    # 正态分布（高斯分布）
    print("正态分布:")
    normal_samples = np.random.normal(loc=0, scale=1, size=5)
    print(f"标准正态分布样本: {normal_samples}")
    
    # 不同参数的正态分布
    normal_custom = np.random.normal(loc=10, scale=2, size=5)
    print(f"N(10, 2²)样本: {normal_custom}")
    
    # 均匀分布
    print(f"\n均匀分布:")
    uniform_samples = np.random.uniform(low=0, high=1, size=5)
    print(f"U(0,1)样本: {uniform_samples}")
    
    uniform_custom = np.random.uniform(low=-5, high=5, size=5)
    print(f"U(-5,5)样本: {uniform_custom}")
    
    # 指数分布
    print(f"\n指数分布:")
    exponential_samples = np.random.exponential(scale=1, size=5)
    print(f"指数分布样本: {exponential_samples}")
    
    # 泊松分布
    print(f"\n泊松分布:")
    poisson_samples = np.random.poisson(lam=3, size=10)
    print(f"泊松分布(λ=3)样本: {poisson_samples}")
    
    # 二项分布
    print(f"\n二项分布:")
    binomial_samples = np.random.binomial(n=10, p=0.3, size=10)
    print(f"二项分布(n=10, p=0.3)样本: {binomial_samples}")
    
    # 多项式分布
    print(f"\n多项式分布:")
    multinomial_sample = np.random.multinomial(n=20, pvals=[0.2, 0.3, 0.5])
    print(f"多项式分布样本: {multinomial_sample}")

def demonstrate_sampling_techniques():
    """演示采样技术"""
    print("\n=== 采样技术 ===")
    
    # 数据集
    data = np.arange(100)
    print(f"原始数据集大小: {len(data)}")
    
    # 简单随机采样
    simple_sample = np.random.choice(data, size=10, replace=False)
    print(f"简单随机采样: {simple_sample}")
    
    # 分层采样（模拟）
    # 假设数据分为3层
    strata = [data[:30], data[30:70], data[70:]]
    stratified_sample = []
    
    for stratum in strata:
        sample_size = len(stratum) // 10  # 每层采样10%
        stratum_sample = np.random.choice(stratum, size=sample_size, replace=False)
        stratified_sample.extend(stratum_sample)
    
    print(f"分层采样结果: {sorted(stratified_sample)}")
    
    # 系统采样
    k = len(data) // 10  # 采样间隔
    start = np.random.randint(0, k)
    systematic_sample = data[start::k]
    print(f"系统采样 (间隔={k}): {systematic_sample}")
    
    # 自助采样 (Bootstrap)
    bootstrap_sample = np.random.choice(data, size=len(data), replace=True)
    unique_elements = len(np.unique(bootstrap_sample))
    print(f"自助采样唯一元素数: {unique_elements}/{len(data)}")

def demonstrate_statistical_analysis():
    """演示统计分析"""
    print("\n=== 统计分析 ===")
    
    # 生成测试数据
    np.random.seed(42)
    data1 = np.random.normal(10, 2, 1000)
    data2 = np.random.normal(12, 3, 1000)
    
    print("数据集1统计:")
    print(f"  均值: {np.mean(data1):.4f}")
    print(f"  标准差: {np.std(data1):.4f}")
    print(f"  偏度: {calculate_skewness(data1):.4f}")
    print(f"  峰度: {calculate_kurtosis(data1):.4f}")
    
    print("\n数据集2统计:")
    print(f"  均值: {np.mean(data2):.4f}")
    print(f"  标准差: {np.std(data2):.4f}")
    print(f"  偏度: {calculate_skewness(data2):.4f}")
    print(f"  峰度: {calculate_kurtosis(data2):.4f}")
    
    # 相关性分析
    correlation = np.corrcoef(data1, data2)[0, 1]
    print(f"\n两数据集相关系数: {correlation:.4f}")
    
    # 分位数分析
    percentiles = [5, 25, 50, 75, 95]
    print(f"\n数据集1分位数:")
    for p in percentiles:
        value = np.percentile(data1, p)
        print(f"  {p}%: {value:.4f}")
    
    # 异常值检测（IQR方法）
    q1, q3 = np.percentile(data1, [25, 75])
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    outliers = data1[(data1 < lower_bound) | (data1 > upper_bound)]
    print(f"\n异常值检测:")
    print(f"  IQR: {iqr:.4f}")
    print(f"  异常值范围: ({lower_bound:.4f}, {upper_bound:.4f})")
    print(f"  异常值数量: {len(outliers)}")

def calculate_skewness(data):
    """计算偏度"""
    mean = np.mean(data)
    std = np.std(data)
    n = len(data)
    skewness = np.sum(((data - mean) / std) ** 3) / n
    return skewness

def calculate_kurtosis(data):
    """计算峰度"""
    mean = np.mean(data)
    std = np.std(data)
    n = len(data)
    kurtosis = np.sum(((data - mean) / std) ** 4) / n - 3
    return kurtosis

def neural_network_initialization():
    """神经网络参数初始化示例"""
    print("\n=== 神经网络参数初始化 ===")
    
    # 网络结构
    input_dim = 784
    hidden_dim = 256
    output_dim = 10
    
    print(f"网络结构: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    # 1. 零初始化（不推荐）
    W1_zeros = np.zeros((input_dim, hidden_dim))
    print(f"零初始化权重统计: 均值={np.mean(W1_zeros):.4f}, 标准差={np.std(W1_zeros):.4f}")
    
    # 2. 随机初始化
    W1_random = np.random.randn(input_dim, hidden_dim) * 0.01
    print(f"小随机数初始化: 均值={np.mean(W1_random):.4f}, 标准差={np.std(W1_random):.4f}")
    
    # 3. Xavier/Glorot初始化
    W1_xavier = np.random.randn(input_dim, hidden_dim) / np.sqrt(input_dim)
    print(f"Xavier初始化: 均值={np.mean(W1_xavier):.4f}, 标准差={np.std(W1_xavier):.4f}")
    
    # 4. He初始化（适用于ReLU）
    W1_he = np.random.randn(input_dim, hidden_dim) * np.sqrt(2.0 / input_dim)
    print(f"He初始化: 均值={np.mean(W1_he):.4f}, 标准差={np.std(W1_he):.4f}")
    
    # 5. 均匀分布初始化
    limit = np.sqrt(6.0 / (input_dim + hidden_dim))
    W1_uniform = np.random.uniform(-limit, limit, (input_dim, hidden_dim))
    print(f"均匀分布初始化: 均值={np.mean(W1_uniform):.4f}, 标准差={np.std(W1_uniform):.4f}")
    
    # 偏置初始化
    b1 = np.zeros(hidden_dim)
    b2 = np.zeros(output_dim)
    print(f"偏置初始化: 全零")

def monte_carlo_simulation():
    """蒙特卡洛模拟示例"""
    print("\n=== 蒙特卡洛模拟 ===")
    
    # 估计π值
    n_samples = 100000
    
    # 在单位正方形内随机生成点
    x = np.random.uniform(-1, 1, n_samples)
    y = np.random.uniform(-1, 1, n_samples)
    
    # 计算在单位圆内的点
    inside_circle = (x**2 + y**2) <= 1
    pi_estimate = 4 * np.sum(inside_circle) / n_samples
    
    print(f"蒙特卡洛估计π值:")
    print(f"  样本数: {n_samples:,}")
    print(f"  圆内点数: {np.sum(inside_circle):,}")
    print(f"  π估计值: {pi_estimate:.6f}")
    print(f"  真实π值: {np.pi:.6f}")
    print(f"  误差: {abs(pi_estimate - np.pi):.6f}")
    
    # 中心极限定理演示
    print(f"\n中心极限定理演示:")
    
    # 从均匀分布抽样
    sample_sizes = [1, 5, 10, 30]
    n_experiments = 1000
    
    for sample_size in sample_sizes:
        # 每次实验抽取sample_size个样本，计算均值
        sample_means = []
        for _ in range(n_experiments):
            samples = np.random.uniform(0, 1, sample_size)
            sample_means.append(np.mean(samples))
        
        sample_means = np.array(sample_means)
        
        print(f"  样本大小={sample_size}:")
        print(f"    样本均值的均值: {np.mean(sample_means):.4f}")
        print(f"    样本均值的标准差: {np.std(sample_means):.4f}")
        print(f"    理论标准差: {1/np.sqrt(12*sample_size):.4f}")

def data_augmentation_example():
    """数据增强示例"""
    print("\n=== 数据增强示例 ===")
    
    # 模拟图像数据
    batch_size = 32
    height, width = 28, 28
    images = np.random.randint(0, 256, (batch_size, height, width))
    
    print(f"原始图像批次形状: {images.shape}")
    
    # 1. 随机旋转（简化版）
    def random_rotation(images, max_angle=15):
        """随机旋转（简化实现）"""
        rotated = images.copy()
        for i in range(len(images)):
            if np.random.random() > 0.5:
                # 简单的90度旋转
                rotated[i] = np.rot90(images[i])
        return rotated
    
    # 2. 随机噪声
    def add_noise(images, noise_level=0.1):
        """添加随机噪声"""
        noise = np.random.normal(0, noise_level * 255, images.shape)
        noisy_images = images + noise
        return np.clip(noisy_images, 0, 255).astype(np.uint8)
    
    # 3. 随机裁剪
    def random_crop(images, crop_size=24):
        """随机裁剪"""
        cropped = np.zeros((len(images), crop_size, crop_size))
        for i in range(len(images)):
            start_h = np.random.randint(0, height - crop_size + 1)
            start_w = np.random.randint(0, width - crop_size + 1)
            cropped[i] = images[i, start_h:start_h+crop_size, start_w:start_w+crop_size]
        return cropped
    
    # 应用数据增强
    rotated_images = random_rotation(images)
    noisy_images = add_noise(images)
    cropped_images = random_crop(images)
    
    print(f"旋转后形状: {rotated_images.shape}")
    print(f"加噪后形状: {noisy_images.shape}")
    print(f"裁剪后形状: {cropped_images.shape}")
    
    # 统计信息
    print(f"\n数据增强统计:")
    print(f"原始图像均值: {np.mean(images):.2f}")
    print(f"加噪图像均值: {np.mean(noisy_images):.2f}")
    print(f"原始图像标准差: {np.std(images):.2f}")
    print(f"加噪图像标准差: {np.std(noisy_images):.2f}")

# 主程序演示
if __name__ == "__main__":
    print("NumPy随机数和统计学习")
    print("=" * 50)
    
    demonstrate_random_number_generation()
    demonstrate_probability_distributions()
    demonstrate_sampling_techniques()
    demonstrate_statistical_analysis()
    neural_network_initialization()
    monte_carlo_simulation()
    data_augmentation_example()
    
    print("\n" + "=" * 50)
    print("NumPy随机数和统计概念总结:")
    print("✓ 随机数生成：各种随机数和随机选择方法")
    print("✓ 概率分布：正态、均匀、指数、泊松等分布")
    print("✓ 采样技术：简单、分层、系统、自助采样")
    print("✓ 统计分析：描述性统计、相关性、异常值检测")
    print("✓ 参数初始化：神经网络权重初始化策略")
    print("✓ 蒙特卡洛：随机模拟和中心极限定理")
    print("✓ 数据增强：图像数据的随机变换")
    print("\n这些技术是机器学习实验和模型训练的重要工具！")
