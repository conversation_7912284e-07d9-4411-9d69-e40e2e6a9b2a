"""
生成器和迭代器 - 高效数据处理
在机器学习中，生成器用于处理大型数据集，避免内存溢出
"""

import random
from typing import Iterator, Generator, List, Tuple

# 1. 基础生成器
def data_generator(data_size: int) -> Generator[float, None, None]:
    """生成随机数据 - 模拟大型数据集"""
    for i in range(data_size):
        # 模拟数据生成过程
        yield random.uniform(-1, 1)

def batch_generator(data: Iterator, batch_size: int) -> Generator[List, None, None]:
    """批次生成器 - 将数据分批处理"""
    batch = []
    for item in data:
        batch.append(item)
        if len(batch) == batch_size:
            yield batch
            batch = []
    
    # 处理最后一个不完整的批次
    if batch:
        yield batch

# 2. 数据预处理生成器
def normalize_data(data: Iterator[float]) -> Generator[float, None, None]:
    """数据标准化生成器"""
    # 第一遍：计算均值和标准差
    data_list = list(data)  # 注意：这里需要将数据转为列表
    mean = sum(data_list) / len(data_list)
    variance = sum((x - mean) ** 2 for x in data_list) / len(data_list)
    std = variance ** 0.5
    
    # 第二遍：标准化
    for x in data_list:
        yield (x - mean) / std if std > 0 else 0

def sliding_window(data: Iterator, window_size: int) -> Generator[List, None, None]:
    """滑动窗口生成器 - 用于时间序列数据"""
    window = []
    for item in data:
        window.append(item)
        if len(window) > window_size:
            window.pop(0)
        if len(window) == window_size:
            yield window.copy()

# 3. 自定义迭代器类
class DataLoader:
    """数据加载器 - 模拟PyTorch的DataLoader"""
    
    def __init__(self, dataset: List, batch_size: int = 32, shuffle: bool = True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
    
    def __iter__(self):
        """返回迭代器"""
        return self._create_iterator()
    
    def __len__(self):
        """返回批次数量"""
        return (len(self.dataset) + self.batch_size - 1) // self.batch_size
    
    def _create_iterator(self):
        """创建迭代器"""
        indices = list(range(len(self.dataset)))
        if self.shuffle:
            random.shuffle(indices)
        
        for i in range(0, len(indices), self.batch_size):
            batch_indices = indices[i:i + self.batch_size]
            batch = [self.dataset[idx] for idx in batch_indices]
            yield batch

# 4. 协程生成器（高级）
def moving_average_coroutine(window_size: int) -> Generator[float, float, None]:
    """移动平均协程 - 演示生成器的send方法"""
    values = []
    while True:
        value = yield
        values.append(value)
        if len(values) > window_size:
            values.pop(0)
        
        # 计算并发送移动平均值
        avg = sum(values) / len(values)
        print(f"接收到 {value:.2f}, 移动平均: {avg:.2f}")

# 5. 生成器表达式和高级用法
def create_training_data(num_samples: int) -> Generator[Tuple[List[float], float], None, None]:
    """创建训练数据 - 特征和标签对"""
    for _ in range(num_samples):
        # 生成随机特征
        features = [random.uniform(-1, 1) for _ in range(3)]
        # 简单的线性关系作为标签
        label = sum(f * w for f, w in zip(features, [0.5, -0.3, 0.8])) + 0.1
        yield features, label

# 演示使用
if __name__ == "__main__":
    print("=== 生成器和迭代器演示 ===")
    
    # 1. 基础生成器
    print("1. 数据生成器:")
    data = data_generator(10)
    print(f"前5个数据: {[next(data) for _ in range(5)]}")
    
    # 2. 批次处理
    print("\n2. 批次生成器:")
    data = data_generator(25)
    batches = batch_generator(data, batch_size=8)
    for i, batch in enumerate(batches):
        print(f"批次 {i+1}: 大小={len(batch)}, 前3个值={batch[:3]}")
    
    # 3. 数据预处理
    print("\n3. 数据标准化:")
    raw_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    normalized = list(normalize_data(iter(raw_data)))
    print(f"原始数据: {raw_data}")
    print(f"标准化后: {[f'{x:.2f}' for x in normalized]}")
    
    # 4. 滑动窗口
    print("\n4. 滑动窗口:")
    time_series = [1, 2, 3, 4, 5, 6, 7, 8]
    windows = list(sliding_window(iter(time_series), window_size=3))
    print(f"时间序列: {time_series}")
    print(f"滑动窗口: {windows}")
    
    # 5. 自定义数据加载器
    print("\n5. 数据加载器:")
    dataset = list(range(50))  # 模拟数据集
    dataloader = DataLoader(dataset, batch_size=12, shuffle=True)
    
    print(f"数据集大小: {len(dataset)}")
    print(f"批次数量: {len(dataloader)}")
    
    for i, batch in enumerate(dataloader):
        if i < 3:  # 只显示前3个批次
            print(f"批次 {i+1}: {batch}")
    
    # 6. 训练数据生成
    print("\n6. 训练数据生成:")
    training_data = create_training_data(5)
    for i, (features, label) in enumerate(training_data):
        print(f"样本 {i+1}: 特征={[f'{f:.2f}' for f in features]}, 标签={label:.2f}")
    
    # 7. 协程演示
    print("\n7. 移动平均协程:")
    ma_coroutine = moving_average_coroutine(3)
    next(ma_coroutine)  # 启动协程
    
    test_values = [1, 2, 3, 4, 5]
    for value in test_values:
        ma_coroutine.send(value)
