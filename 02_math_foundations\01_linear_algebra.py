"""
线性代数基础 - 机器学习的数学基石
重点理解向量、矩阵运算，这些是Transformer中注意力机制的基础

关键概念：
1. 向量：表示数据点（如词嵌入向量）
2. 矩阵：表示线性变换（如权重矩阵）
3. 点积：计算相似度（注意力分数）
4. 矩阵乘法：神经网络的核心运算
"""

import math
from typing import List, Tuple, Union

class Vector:
    """向量类 - 理解向量的基本概念和运算"""
    
    def __init__(self, components: List[float]):
        self.components = components
        self.dim = len(components)
    
    def __repr__(self):
        return f"Vector({self.components})"
    
    def __add__(self, other):
        """向量加法"""
        if self.dim != other.dim:
            raise ValueError("向量维度必须相同")
        return Vector([a + b for a, b in zip(self.components, other.components)])
    
    def __sub__(self, other):
        """向量减法"""
        if self.dim != other.dim:
            raise ValueError("向量维度必须相同")
        return Vector([a - b for a, b in zip(self.components, other.components)])
    
    def __mul__(self, scalar: float):
        """标量乘法"""
        return Vector([scalar * x for x in self.components])
    
    def __rmul__(self, scalar: float):
        """右乘标量"""
        return self.__mul__(scalar)
    
    def dot(self, other) -> float:
        """点积（内积）- 在注意力机制中用于计算相似度"""
        if self.dim != other.dim:
            raise ValueError("向量维度必须相同")
        return sum(a * b for a, b in zip(self.components, other.components))
    
    def magnitude(self) -> float:
        """向量的模长（长度）"""
        return math.sqrt(sum(x**2 for x in self.components))
    
    def normalize(self):
        """向量标准化 - 在注意力机制中很重要"""
        mag = self.magnitude()
        if mag == 0:
            return Vector([0] * self.dim)
        return Vector([x / mag for x in self.components])
    
    def cosine_similarity(self, other) -> float:
        """余弦相似度 - 衡量向量方向的相似性"""
        dot_product = self.dot(other)
        magnitude_product = self.magnitude() * other.magnitude()
        if magnitude_product == 0:
            return 0
        return dot_product / magnitude_product

class Matrix:
    """矩阵类 - 理解矩阵运算，Transformer中的核心操作"""
    
    def __init__(self, data: List[List[float]]):
        self.data = data
        self.rows = len(data)
        self.cols = len(data[0]) if data else 0
        
        # 验证矩阵格式
        for row in data:
            if len(row) != self.cols:
                raise ValueError("矩阵的每行必须有相同的列数")
    
    def __repr__(self):
        return f"Matrix({self.rows}x{self.cols})"
    
    def __getitem__(self, index):
        return self.data[index]
    
    def __add__(self, other):
        """矩阵加法"""
        if self.rows != other.rows or self.cols != other.cols:
            raise ValueError("矩阵维度必须相同")
        
        result = []
        for i in range(self.rows):
            row = [self.data[i][j] + other.data[i][j] for j in range(self.cols)]
            result.append(row)
        return Matrix(result)
    
    def __mul__(self, other):
        """矩阵乘法 - Transformer中最重要的操作之一"""
        if isinstance(other, (int, float)):
            # 标量乘法
            result = [[self.data[i][j] * other for j in range(self.cols)] 
                     for i in range(self.rows)]
            return Matrix(result)
        
        elif isinstance(other, Matrix):
            # 矩阵乘法
            if self.cols != other.rows:
                raise ValueError(f"矩阵维度不匹配: {self.cols} != {other.rows}")
            
            result = []
            for i in range(self.rows):
                row = []
                for j in range(other.cols):
                    # 计算第i行第j列的元素
                    element = sum(self.data[i][k] * other.data[k][j] 
                                for k in range(self.cols))
                    row.append(element)
                result.append(row)
            return Matrix(result)
        
        else:
            raise TypeError("不支持的乘法类型")
    
    def transpose(self):
        """矩阵转置 - 在注意力机制中用于Key矩阵"""
        result = [[self.data[i][j] for i in range(self.rows)] 
                 for j in range(self.cols)]
        return Matrix(result)
    
    def get_row(self, index: int) -> Vector:
        """获取指定行作为向量"""
        return Vector(self.data[index])
    
    def get_col(self, index: int) -> Vector:
        """获取指定列作为向量"""
        return Vector([self.data[i][index] for i in range(self.rows)])
    
    def apply_function(self, func):
        """对矩阵的每个元素应用函数"""
        result = [[func(self.data[i][j]) for j in range(self.cols)] 
                 for i in range(self.rows)]
        return Matrix(result)

def softmax(vector: Vector) -> Vector:
    """Softmax函数 - 注意力机制的核心"""
    # 为了数值稳定性，减去最大值
    max_val = max(vector.components)
    exp_values = [math.exp(x - max_val) for x in vector.components]
    sum_exp = sum(exp_values)
    return Vector([x / sum_exp for x in exp_values])

def attention_mechanism_simple(query: Vector, keys: List[Vector], values: List[Vector]) -> Vector:
    """简化的注意力机制 - 理解Transformer的核心概念"""
    if len(keys) != len(values):
        raise ValueError("Keys和Values的数量必须相同")
    
    # 1. 计算注意力分数（Query与每个Key的点积）
    scores = [query.dot(key) for key in keys]
    print(f"注意力分数: {[f'{s:.3f}' for s in scores]}")
    
    # 2. 应用Softmax得到注意力权重
    attention_weights = softmax(Vector(scores))
    print(f"注意力权重: {[f'{w:.3f}' for w in attention_weights.components]}")
    
    # 3. 加权求和Values
    result_components = [0.0] * values[0].dim
    for i, weight in enumerate(attention_weights.components):
        for j in range(values[i].dim):
            result_components[j] += weight * values[i].components[j]
    
    return Vector(result_components)

def matrix_vector_multiply(matrix: Matrix, vector: Vector) -> Vector:
    """矩阵向量乘法"""
    if matrix.cols != vector.dim:
        raise ValueError("矩阵列数必须等于向量维度")
    
    result = []
    for i in range(matrix.rows):
        element = sum(matrix.data[i][j] * vector.components[j] 
                     for j in range(matrix.cols))
        result.append(element)
    
    return Vector(result)

# 演示使用
if __name__ == "__main__":
    print("=== 线性代数基础演示 ===")
    
    # 1. 向量运算
    print("1. 向量运算:")
    v1 = Vector([1, 2, 3])
    v2 = Vector([4, 5, 6])
    
    print(f"v1 = {v1}")
    print(f"v2 = {v2}")
    print(f"v1 + v2 = {v1 + v2}")
    print(f"v1 - v2 = {v1 - v2}")
    print(f"2 * v1 = {2 * v1}")
    print(f"v1 · v2 = {v1.dot(v2)}")
    print(f"|v1| = {v1.magnitude():.3f}")
    print(f"v1标准化 = {v1.normalize()}")
    print(f"余弦相似度 = {v1.cosine_similarity(v2):.3f}")
    
    # 2. 矩阵运算
    print("\n2. 矩阵运算:")
    A = Matrix([[1, 2], [3, 4]])
    B = Matrix([[5, 6], [7, 8]])
    
    print(f"矩阵A: {A.rows}x{A.cols}")
    for row in A.data:
        print(f"  {row}")
    
    print(f"矩阵B: {B.rows}x{B.cols}")
    for row in B.data:
        print(f"  {row}")
    
    # 矩阵乘法
    C = A * B
    print(f"A * B:")
    for row in C.data:
        print(f"  {row}")
    
    # 矩阵转置
    A_T = A.transpose()
    print(f"A的转置:")
    for row in A_T.data:
        print(f"  {row}")
    
    # 3. 矩阵向量乘法
    print("\n3. 矩阵向量乘法:")
    matrix = Matrix([[1, 2, 3], [4, 5, 6]])
    vector = Vector([1, 0, -1])
    
    result = matrix_vector_multiply(matrix, vector)
    print(f"矩阵: {matrix.rows}x{matrix.cols}")
    print(f"向量: {vector}")
    print(f"结果: {result}")
    
    # 4. 简化的注意力机制演示
    print("\n4. 简化的注意力机制:")
    
    # 假设我们有一个查询和三个键值对
    query = Vector([1, 0, 1])  # 查询向量
    keys = [
        Vector([1, 1, 0]),     # 键1
        Vector([0, 1, 1]),     # 键2  
        Vector([1, 0, 1])      # 键3
    ]
    values = [
        Vector([2, 0]),        # 值1
        Vector([0, 3]),        # 值2
        Vector([1, 1])         # 值3
    ]
    
    print(f"查询: {query}")
    print(f"键: {keys}")
    print(f"值: {values}")
    
    attention_output = attention_mechanism_simple(query, keys, values)
    print(f"注意力输出: {attention_output}")
    
    # 5. Softmax演示
    print("\n5. Softmax函数:")
    scores = Vector([2.0, 1.0, 0.1])
    softmax_result = softmax(scores)
    print(f"输入分数: {scores}")
    print(f"Softmax结果: {softmax_result}")
    print(f"概率和: {sum(softmax_result.components):.6f}")
    
    print("\n=== 关键概念总结 ===")
    print("✓ 向量：表示数据点，如词嵌入")
    print("✓ 点积：衡量向量相似度，注意力分数的基础")
    print("✓ 矩阵乘法：神经网络中的线性变换")
    print("✓ 转置：在注意力机制中用于Key矩阵")
    print("✓ Softmax：将分数转换为概率分布")
    print("✓ 注意力机制：Query、Key、Value的交互")
