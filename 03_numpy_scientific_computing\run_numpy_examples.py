"""
运行所有NumPy示例
这个脚本会依次运行所有的NumPy学习示例，帮助你掌握科学计算基础
"""

import sys
import os
import importlib.util

def run_module(module_path, module_name):
    """动态运行模块"""
    print(f"\n{'='*60}")
    print(f"运行 {module_name}")
    print(f"{'='*60}")
    
    try:
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print(f"\n✓ {module_name} 运行成功!")
        
    except Exception as e:
        print(f"\n✗ {module_name} 运行失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("NumPy和科学计算基础 - 示例代码运行器")
    print("这将帮助你逐步掌握NumPy的核心功能")
    
    # 要运行的模块列表
    modules = [
        ("01_numpy_basics.py", "NumPy基础"),
        ("02_linear_algebra.py", "线性代数运算"),
        ("03_array_manipulation.py", "数组操作"),
        ("04_random_and_statistics.py", "随机数和统计"),
        ("numpy_practice_project.py", "NumPy综合实践项目")
    ]
    
    success_count = 0
    total_count = len(modules)
    
    for module_file, module_desc in modules:
        module_path = os.path.join(os.path.dirname(__file__), module_file)
        
        if os.path.exists(module_path):
            if run_module(module_path, module_desc):
                success_count += 1
        else:
            print(f"\n✗ 文件不存在: {module_file}")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"运行总结")
    print(f"{'='*60}")
    print(f"成功运行: {success_count}/{total_count} 个模块")
    
    if success_count == total_count:
        print("\n🎉 恭喜！所有NumPy示例都运行成功！")
        print("你已经掌握了NumPy和科学计算基础，可以进入下一阶段的学习了。")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个模块运行失败，请检查代码。")
    
    print("\nNumPy技能掌握检查清单:")
    print("□ 数组创建和基本属性")
    print("□ 索引、切片和高级索引")
    print("□ 数组运算和广播机制")
    print("□ 线性代数运算（矩阵乘法、特征值等）")
    print("□ 数组形状操作（reshape、transpose等）")
    print("□ 数组合并和分割")
    print("□ 统计和聚合操作")
    print("□ 随机数生成和概率分布")
    print("□ 向量化操作和性能优化")
    print("□ 神经网络中的NumPy应用")
    
    print("\n与深度学习的联系:")
    print("• NumPy数组 → PyTorch张量")
    print("• 矩阵乘法 → 神经网络前向传播")
    print("• 广播机制 → 张量运算")
    print("• 随机初始化 → 权重初始化")
    print("• 统计函数 → 损失计算和评估")
    
    print("\n下一步学习建议:")
    print("1. 熟练掌握NumPy的向量化思维")
    print("2. 理解广播机制的规则和应用")
    print("3. 练习线性代数运算的NumPy实现")
    print("4. 体验NumPy相比纯Python的性能优势")
    print("5. 准备进入机器学习基础学习阶段")

if __name__ == "__main__":
    main()
