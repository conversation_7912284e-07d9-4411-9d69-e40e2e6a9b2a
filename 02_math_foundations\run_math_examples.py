"""
运行所有数学基础示例
这个脚本会依次运行所有的数学概念示例，帮助你理解每个数学概念
"""

import sys
import os
import importlib.util

def run_module(module_path, module_name):
    """动态运行模块"""
    print(f"\n{'='*60}")
    print(f"运行 {module_name}")
    print(f"{'='*60}")
    
    try:
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print(f"\n✓ {module_name} 运行成功!")
        
    except Exception as e:
        print(f"\n✗ {module_name} 运行失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("机器学习数学基础 - 示例代码运行器")
    print("这将帮助你逐步理解每个数学概念")
    
    # 要运行的模块列表
    modules = [
        ("01_linear_algebra.py", "线性代数基础"),
        ("02_calculus.py", "微积分基础"),
        ("03_probability_statistics.py", "概率论和统计"),
        ("math_practice_project.py", "数学基础综合项目")
    ]
    
    success_count = 0
    total_count = len(modules)
    
    for module_file, module_desc in modules:
        module_path = os.path.join(os.path.dirname(__file__), module_file)
        
        if os.path.exists(module_path):
            if run_module(module_path, module_desc):
                success_count += 1
        else:
            print(f"\n✗ 文件不存在: {module_file}")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"运行总结")
    print(f"{'='*60}")
    print(f"成功运行: {success_count}/{total_count} 个模块")
    
    if success_count == total_count:
        print("\n🎉 恭喜！所有数学基础示例都运行成功！")
        print("你已经掌握了机器学习的数学基础，可以进入下一阶段的学习了。")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个模块运行失败，请检查代码。")
    
    print("\n数学概念掌握检查清单:")
    print("□ 向量和矩阵运算")
    print("□ 点积和矩阵乘法")
    print("□ 导数和偏导数")
    print("□ 梯度和梯度下降")
    print("□ 链式法则")
    print("□ 概率和条件概率")
    print("□ 随机变量和分布")
    print("□ 期望、方差、协方差")
    print("□ 信息熵和交叉熵")
    print("□ 最大似然估计")
    
    print("\n下一步学习建议:")
    print("1. 确保理解每个数学概念的几何和直观意义")
    print("2. 练习手工计算简单的例子")
    print("3. 理解这些概念在机器学习中的应用")
    print("4. 准备进入NumPy科学计算学习阶段")

if __name__ == "__main__":
    main()
