"""
Python面向对象编程进阶
重点：理解类的特殊方法，为后续实现神经网络组件做准备
"""

class Neuron:
    """
    简单神经元类 - 演示面向对象编程概念
    这将是我们构建神经网络的基础组件
    """
    
    def __init__(self, weights, bias=0.0):
        """初始化神经元"""
        self.weights = weights
        self.bias = bias
        self.last_input = None
        self.last_output = None
    
    def __call__(self, inputs):
        """让对象可调用 - 重要的魔法方法"""
        return self.forward(inputs)
    
    def __repr__(self):
        """对象的字符串表示"""
        return f"Neuron(weights={self.weights}, bias={self.bias})"
    
    def __len__(self):
        """返回权重数量"""
        return len(self.weights)
    
    def forward(self, inputs):
        """前向传播"""
        if len(inputs) != len(self.weights):
            raise ValueError("输入维度与权重维度不匹配")
        
        # 计算加权和
        weighted_sum = sum(w * x for w, x in zip(self.weights, inputs))
        output = weighted_sum + self.bias
        
        # 保存用于反向传播
        self.last_input = inputs
        self.last_output = output
        
        return output
    
    @property
    def num_parameters(self):
        """属性装饰器 - 返回参数数量"""
        return len(self.weights) + 1  # 权重 + 偏置
    
    @staticmethod
    def sigmoid(x):
        """静态方法 - 激活函数"""
        import math
        return 1 / (1 + math.exp(-x))
    
    @classmethod
    def create_random(cls, input_size):
        """类方法 - 创建随机初始化的神经元"""
        import random
        weights = [random.uniform(-1, 1) for _ in range(input_size)]
        bias = random.uniform(-1, 1)
        return cls(weights, bias)


class Layer:
    """神经网络层 - 演示继承和组合"""
    
    def __init__(self, neurons):
        self.neurons = neurons
    
    def __iter__(self):
        """使层可迭代"""
        return iter(self.neurons)
    
    def __getitem__(self, index):
        """支持索引访问"""
        return self.neurons[index]
    
    def forward(self, inputs):
        """层的前向传播"""
        return [neuron(inputs) for neuron in self.neurons]


# 演示使用
if __name__ == "__main__":
    # 创建神经元
    neuron = Neuron([0.5, -0.3, 0.8], bias=0.1)
    print(f"神经元: {neuron}")
    print(f"参数数量: {neuron.num_parameters}")
    
    # 前向传播
    inputs = [1.0, 2.0, -1.0]
    output = neuron(inputs)  # 使用__call__方法
    print(f"输入: {inputs}")
    print(f"输出: {output}")
    
    # 创建随机神经元
    random_neuron = Neuron.create_random(3)
    print(f"随机神经元: {random_neuron}")
    
    # 创建层
    neurons = [Neuron.create_random(3) for _ in range(4)]
    layer = Layer(neurons)
    
    # 层的前向传播
    layer_output = layer.forward(inputs)
    print(f"层输出: {layer_output}")
    
    # 迭代层中的神经元
    print("层中的神经元:")
    for i, neuron in enumerate(layer):
        print(f"  神经元 {i}: {neuron}")
