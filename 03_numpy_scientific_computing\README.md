# NumPy和科学计算基础

## 学习目标
掌握NumPy数组操作和科学计算，为深度学习框架的使用打下基础。NumPy是Python科学计算的基石，也是理解PyTorch张量操作的前提。

## 学习内容

### 1. NumPy数组基础
- **数组创建和属性**
  - 创建数组的各种方法
  - 数组的形状、维度、数据类型
  - 数组的内存布局
- **数组索引和切片**
  - 基础索引和高级索引
  - 布尔索引和花式索引
  - 多维数组的切片操作

### 2. 数组运算
- **元素级运算**
  - 算术运算、比较运算
  - 通用函数(ufunc)
  - 数学函数应用
- **广播机制**
  - 广播规则和原理
  - 不同形状数组的运算
  - 广播在机器学习中的应用

### 3. 线性代数运算
- **矩阵运算**
  - 矩阵乘法、转置、逆矩阵
  - 特征值和特征向量
  - 奇异值分解(SVD)
- **向量运算**
  - 点积、叉积、范数
  - 向量化操作的优势

### 4. 数组变形和合并
- **形状操作**
  - reshape、flatten、ravel
  - 维度扩展和压缩
  - 轴的概念和操作
- **数组合并和分割**
  - concatenate、stack、split
  - 水平和垂直合并

### 5. 统计和聚合操作
- **描述性统计**
  - 均值、方差、标准差
  - 最大值、最小值、分位数
  - 沿指定轴的统计
- **聚合函数**
  - sum、mean、std等
  - 条件聚合和分组操作

### 6. 随机数生成
- **随机数模块**
  - 各种分布的随机数生成
  - 随机种子设置
  - 随机采样和洗牌

## 实践项目
1. 实现矩阵运算库
2. 构建数据预处理工具
3. 实现简单的神经网络前向传播
4. 图像数据的NumPy操作

## 学习时间
预计 4-6 天

## 重点关注
- **向量化操作** - 避免Python循环，提高计算效率
- **广播机制** - 理解不同形状数组的运算规则
- **轴的概念** - 多维数组操作的核心
- **内存效率** - 理解视图vs拷贝，优化内存使用

## 与深度学习的联系
- NumPy数组 → PyTorch张量
- 广播机制 → 张量广播
- 线性代数运算 → 神经网络计算
- 向量化操作 → GPU并行计算

## 下一步
完成后进入机器学习基础学习阶段
