"""
Python高级特性综合实践项目
构建一个简单的神经网络框架，综合运用所有学到的高级特性
"""

import random
import math
import time
import contextlib
from typing import List, Callable, Optional, Iterator, Tuple, Any
from abc import ABC, abstractmethod

# 1. 基础组件 - 使用面向对象编程
class Tensor:
    """简单张量类 - 演示魔法方法"""
    
    def __init__(self, data: List[float], shape: Optional[Tuple[int, ...]] = None):
        self.data = data
        self.shape = shape or (len(data),)
        self.grad = None
    
    def __repr__(self):
        return f"Tensor(data={self.data}, shape={self.shape})"
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, index):
        return self.data[index]
    
    def __setitem__(self, index, value):
        self.data[index] = value
    
    def __add__(self, other):
        if isinstance(other, Tensor):
            return Tensor([a + b for a, b in zip(self.data, other.data)])
        return Tensor([x + other for x in self.data])
    
    def __mul__(self, other):
        if isinstance(other, Tensor):
            return Tensor([a * b for a, b in zip(self.data, other.data)])
        return Tensor([x * other for x in self.data])
    
    def __iter__(self):
        return iter(self.data)

# 2. 激活函数 - 使用函数式编程
class ActivationFunctions:
    """激活函数集合"""
    
    @staticmethod
    def sigmoid(x: float) -> float:
        return 1 / (1 + math.exp(-max(-500, min(500, x))))
    
    @staticmethod
    def relu(x: float) -> float:
        return max(0, x)
    
    @staticmethod
    def tanh(x: float) -> float:
        return math.tanh(x)
    
    @staticmethod
    def apply_activation(activation_fn: Callable[[float], float]):
        """高阶函数 - 返回应用激活函数的函数"""
        def activate(tensor: Tensor) -> Tensor:
            return Tensor([activation_fn(x) for x in tensor.data])
        return activate

# 3. 装饰器用于功能增强
def track_computation_time(func):
    """计算时间跟踪装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        return result
    return wrapper

def validate_tensor_input(func):
    """张量输入验证装饰器"""
    def wrapper(self, tensor: Tensor, *args, **kwargs):
        if not isinstance(tensor, Tensor):
            raise TypeError("输入必须是Tensor类型")
        if len(tensor) != len(self.weights):
            raise ValueError(f"输入维度 {len(tensor)} 与权重维度 {len(self.weights)} 不匹配")
        return func(self, tensor, *args, **kwargs)
    return wrapper

class Cache:
    """缓存装饰器类"""
    def __init__(self, maxsize=128):
        self.maxsize = maxsize
        self.cache = {}
    
    def __call__(self, func):
        def wrapper(*args, **kwargs):
            key = str(args) + str(sorted(kwargs.items()))
            if key in self.cache:
                return self.cache[key]
            
            result = func(*args, **kwargs)
            if len(self.cache) >= self.maxsize:
                # 简单的FIFO策略
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            self.cache[key] = result
            return result
        return wrapper

# 4. 神经网络层 - 综合运用各种特性
class Layer(ABC):
    """抽象层基类"""
    
    @abstractmethod
    def forward(self, input_tensor: Tensor) -> Tensor:
        pass
    
    @abstractmethod
    def backward(self, grad_output: Tensor) -> Tensor:
        pass

class DenseLayer(Layer):
    """全连接层"""
    
    def __init__(self, input_size: int, output_size: int, activation: str = "relu"):
        self.input_size = input_size
        self.output_size = output_size
        
        # 随机初始化权重和偏置
        self.weights = [[random.uniform(-1, 1) for _ in range(input_size)] 
                       for _ in range(output_size)]
        self.biases = [random.uniform(-1, 1) for _ in range(output_size)]
        
        # 设置激活函数
        activation_map = {
            "relu": ActivationFunctions.relu,
            "sigmoid": ActivationFunctions.sigmoid,
            "tanh": ActivationFunctions.tanh
        }
        self.activation_fn = activation_map[activation]
        self.activate = ActivationFunctions.apply_activation(self.activation_fn)
        
        # 缓存用于反向传播
        self.last_input = None
        self.last_output = None
    
    @track_computation_time
    def forward(self, input_tensor: Tensor) -> Tensor:
        """前向传播"""
        if len(input_tensor) != self.input_size:
            raise ValueError(f"输入维度不匹配: 期望 {self.input_size}, 得到 {len(input_tensor)}")
        
        # 计算线性变换
        outputs = []
        for i in range(self.output_size):
            weighted_sum = sum(w * x for w, x in zip(self.weights[i], input_tensor.data))
            outputs.append(weighted_sum + self.biases[i])
        
        linear_output = Tensor(outputs)
        activated_output = self.activate(linear_output)
        
        # 缓存用于反向传播
        self.last_input = input_tensor
        self.last_output = activated_output
        
        return activated_output
    
    def backward(self, grad_output: Tensor) -> Tensor:
        """反向传播（简化版）"""
        # 这里只是示例，实际的反向传播更复杂
        print(f"反向传播通过 {self.__class__.__name__}")
        return grad_output
    
    @property
    def num_parameters(self):
        """参数数量"""
        return self.input_size * self.output_size + self.output_size

# 5. 数据加载器 - 使用生成器和迭代器
class DataLoader:
    """数据加载器"""
    
    def __init__(self, dataset: List[Tuple[List[float], List[float]]], 
                 batch_size: int = 32, shuffle: bool = True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
    
    def __iter__(self) -> Iterator[List[Tuple[Tensor, Tensor]]]:
        """返回批次迭代器"""
        indices = list(range(len(self.dataset)))
        if self.shuffle:
            random.shuffle(indices)
        
        for i in range(0, len(indices), self.batch_size):
            batch_indices = indices[i:i + self.batch_size]
            batch = []
            for idx in batch_indices:
                features, labels = self.dataset[idx]
                batch.append((Tensor(features), Tensor(labels)))
            yield batch
    
    def __len__(self):
        return (len(self.dataset) + self.batch_size - 1) // self.batch_size

def create_synthetic_data(num_samples: int, input_dim: int) -> List[Tuple[List[float], List[float]]]:
    """生成合成数据"""
    dataset = []
    for _ in range(num_samples):
        # 随机特征
        features = [random.uniform(-1, 1) for _ in range(input_dim)]
        # 简单的线性关系作为标签
        label = [sum(features) > 0]  # 二分类
        dataset.append((features, label))
    return dataset

# 6. 训练上下文管理器
@contextlib.contextmanager
def training_session(model, device="cpu"):
    """训练会话上下文管理器"""
    print(f"开始训练会话 (设备: {device})")
    start_time = time.time()
    
    try:
        # 设置训练模式
        if hasattr(model, 'training'):
            old_mode = model.training
            model.training = True
        
        yield model
        
    finally:
        # 恢复模式
        if hasattr(model, 'training'):
            model.training = old_mode
        
        end_time = time.time()
        print(f"训练会话结束，总时间: {end_time - start_time:.4f}秒")

# 7. 简单神经网络
class SimpleNeuralNetwork:
    """简单神经网络"""
    
    def __init__(self, layer_sizes: List[int]):
        self.layers = []
        self.training = True
        
        # 构建层
        for i in range(len(layer_sizes) - 1):
            layer = DenseLayer(layer_sizes[i], layer_sizes[i + 1])
            self.layers.append(layer)
    
    def forward(self, input_tensor: Tensor) -> Tensor:
        """前向传播"""
        current = input_tensor
        for layer in self.layers:
            current = layer.forward(current)
        return current
    
    def __call__(self, input_tensor: Tensor) -> Tensor:
        """使网络可调用"""
        return self.forward(input_tensor)
    
    @property
    def num_parameters(self):
        """总参数数量"""
        return sum(layer.num_parameters for layer in self.layers)

# 8. 训练函数 - 综合运用所有特性
@Cache(maxsize=64)
def compute_loss(predictions: List[float], targets: List[float]) -> float:
    """计算损失（带缓存）"""
    return sum((p - t) ** 2 for p, t in zip(predictions, targets)) / len(predictions)

def train_model(model: SimpleNeuralNetwork, dataloader: DataLoader, epochs: int = 5):
    """训练模型"""
    
    with training_session(model) as training_model:
        for epoch in range(epochs):
            total_loss = 0.0
            num_batches = 0
            
            print(f"\nEpoch {epoch + 1}/{epochs}")
            
            for batch in dataloader:
                batch_loss = 0.0
                
                for features, labels in batch:
                    # 前向传播
                    predictions = training_model(features)
                    
                    # 计算损失
                    loss = compute_loss(predictions.data, labels.data)
                    batch_loss += loss
                
                total_loss += batch_loss / len(batch)
                num_batches += 1
            
            avg_loss = total_loss / num_batches
            print(f"平均损失: {avg_loss:.4f}")

# 演示使用
if __name__ == "__main__":
    print("=== Python高级特性综合实践 ===")
    
    # 1. 创建数据
    print("1. 创建合成数据集...")
    dataset = create_synthetic_data(num_samples=1000, input_dim=4)
    dataloader = DataLoader(dataset, batch_size=32, shuffle=True)
    
    print(f"数据集大小: {len(dataset)}")
    print(f"批次数量: {len(dataloader)}")
    
    # 2. 创建模型
    print("\n2. 创建神经网络...")
    model = SimpleNeuralNetwork([4, 8, 4, 1])  # 4输入 -> 8隐藏 -> 4隐藏 -> 1输出
    print(f"模型参数数量: {model.num_parameters}")
    
    # 3. 测试前向传播
    print("\n3. 测试前向传播...")
    test_input = Tensor([1.0, -0.5, 0.8, -0.2])
    output = model(test_input)
    print(f"测试输入: {test_input.data}")
    print(f"模型输出: {output.data}")
    
    # 4. 训练模型
    print("\n4. 开始训练...")
    train_model(model, dataloader, epochs=3)
    
    # 5. 展示缓存效果
    print(f"\n5. 损失函数缓存大小: {len(compute_loss.cache)}")
    
    print("\n=== 实践项目完成 ===")
    print("你已经成功运用了以下Python高级特性:")
    print("✓ 面向对象编程（类、继承、魔法方法）")
    print("✓ 装饰器（函数装饰器、类装饰器、带参数装饰器）")
    print("✓ 生成器和迭代器（数据加载、批处理）")
    print("✓ 上下文管理器（资源管理、状态控制）")
    print("✓ 函数式编程（高阶函数、闭包）")
    print("✓ 属性装饰器和静态方法")
    print("✓ 抽象基类和多态")
