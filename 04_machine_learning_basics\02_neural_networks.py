"""
神经网络基础 - 从感知机到多层感知机
理解神经网络的基本原理，为深度学习打下基础
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Callable

class Perceptron:
    """单层感知机实现"""
    
    def __init__(self, learning_rate: float = 0.1, max_iterations: int = 1000):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.weights = None
        self.bias = None
        self.errors = []
    
    def activation_function(self, x: float) -> int:
        """阶跃激活函数"""
        return 1 if x >= 0 else 0
    
    def fit(self, X: np.ndarray, y: np.ndarray):
        """训练感知机"""
        n_samples, n_features = X.shape
        
        # 初始化权重和偏置
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0
        self.errors = []
        
        for iteration in range(self.max_iterations):
            errors = 0
            
            for i in range(n_samples):
                # 计算线性输出
                linear_output = np.dot(X[i], self.weights) + self.bias
                
                # 应用激活函数
                prediction = self.activation_function(linear_output)
                
                # 计算误差
                error = y[i] - prediction
                
                if error != 0:
                    # 更新权重和偏置
                    self.weights += self.learning_rate * error * X[i]
                    self.bias += self.learning_rate * error
                    errors += 1
            
            self.errors.append(errors)
            
            # 如果没有错误，则收敛
            if errors == 0:
                print(f"感知机在第 {iteration + 1} 次迭代后收敛")
                break
        
        if errors > 0:
            print(f"感知机在 {self.max_iterations} 次迭代后未完全收敛")
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        linear_output = X @ self.weights + self.bias
        return np.array([self.activation_function(x) for x in linear_output])
    
    def score(self, X: np.ndarray, y: np.ndarray) -> float:
        """计算准确率"""
        predictions = self.predict(X)
        return np.mean(predictions == y)

class ActivationFunctions:
    """激活函数集合"""
    
    @staticmethod
    def sigmoid(x: np.ndarray) -> np.ndarray:
        """Sigmoid函数"""
        x = np.clip(x, -500, 500)  # 防止溢出
        return 1 / (1 + np.exp(-x))
    
    @staticmethod
    def sigmoid_derivative(x: np.ndarray) -> np.ndarray:
        """Sigmoid导数"""
        s = ActivationFunctions.sigmoid(x)
        return s * (1 - s)
    
    @staticmethod
    def tanh(x: np.ndarray) -> np.ndarray:
        """Tanh函数"""
        return np.tanh(x)
    
    @staticmethod
    def tanh_derivative(x: np.ndarray) -> np.ndarray:
        """Tanh导数"""
        return 1 - np.tanh(x) ** 2
    
    @staticmethod
    def relu(x: np.ndarray) -> np.ndarray:
        """ReLU函数"""
        return np.maximum(0, x)
    
    @staticmethod
    def relu_derivative(x: np.ndarray) -> np.ndarray:
        """ReLU导数"""
        return (x > 0).astype(float)
    
    @staticmethod
    def leaky_relu(x: np.ndarray, alpha: float = 0.01) -> np.ndarray:
        """Leaky ReLU函数"""
        return np.where(x > 0, x, alpha * x)
    
    @staticmethod
    def leaky_relu_derivative(x: np.ndarray, alpha: float = 0.01) -> np.ndarray:
        """Leaky ReLU导数"""
        return np.where(x > 0, 1, alpha)

class MultiLayerPerceptron:
    """多层感知机实现"""
    
    def __init__(self, layer_sizes: List[int], activation: str = 'sigmoid', 
                 learning_rate: float = 0.01, max_iterations: int = 1000):
        """
        初始化多层感知机
        
        Args:
            layer_sizes: 每层神经元数量，如[2, 4, 3, 1]
            activation: 激活函数类型
            learning_rate: 学习率
            max_iterations: 最大迭代次数
        """
        self.layer_sizes = layer_sizes
        self.num_layers = len(layer_sizes)
        self.activation = activation
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        
        # 初始化权重和偏置
        self.weights = []
        self.biases = []
        
        for i in range(self.num_layers - 1):
            # Xavier初始化
            w = np.random.randn(layer_sizes[i], layer_sizes[i+1]) / np.sqrt(layer_sizes[i])
            b = np.zeros((1, layer_sizes[i+1]))
            
            self.weights.append(w)
            self.biases.append(b)
        
        # 选择激活函数
        self.activation_func, self.activation_derivative = self._get_activation_functions()
        
        # 训练历史
        self.loss_history = []
    
    def _get_activation_functions(self) -> Tuple[Callable, Callable]:
        """获取激活函数和其导数"""
        if self.activation == 'sigmoid':
            return ActivationFunctions.sigmoid, ActivationFunctions.sigmoid_derivative
        elif self.activation == 'tanh':
            return ActivationFunctions.tanh, ActivationFunctions.tanh_derivative
        elif self.activation == 'relu':
            return ActivationFunctions.relu, ActivationFunctions.relu_derivative
        else:
            raise ValueError(f"不支持的激活函数: {self.activation}")
    
    def forward_propagation(self, X: np.ndarray) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """前向传播"""
        activations = [X]  # 存储每层的激活值
        z_values = []      # 存储每层的线性输出
        
        current_input = X
        
        for i in range(self.num_layers - 1):
            # 线性变换: z = X @ W + b
            z = current_input @ self.weights[i] + self.biases[i]
            z_values.append(z)
            
            # 激活函数
            if i == self.num_layers - 2:  # 输出层
                if self.layer_sizes[-1] == 1:  # 二分类
                    a = ActivationFunctions.sigmoid(z)
                else:  # 多分类
                    a = self.softmax(z)
            else:  # 隐藏层
                a = self.activation_func(z)
            
            activations.append(a)
            current_input = a
        
        return activations, z_values
    
    def softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)
    
    def backward_propagation(self, X: np.ndarray, y: np.ndarray, 
                           activations: List[np.ndarray], z_values: List[np.ndarray]) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """反向传播"""
        m = X.shape[0]  # 样本数量
        
        # 初始化梯度
        weight_gradients = [np.zeros_like(w) for w in self.weights]
        bias_gradients = [np.zeros_like(b) for b in self.biases]
        
        # 输出层误差
        if self.layer_sizes[-1] == 1:  # 二分类
            delta = activations[-1] - y.reshape(-1, 1)
        else:  # 多分类
            delta = activations[-1] - y
        
        # 从输出层向输入层反向传播
        for i in range(self.num_layers - 2, -1, -1):
            # 计算权重和偏置梯度
            weight_gradients[i] = activations[i].T @ delta / m
            bias_gradients[i] = np.mean(delta, axis=0, keepdims=True)
            
            # 计算前一层的误差（如果不是输入层）
            if i > 0:
                delta = (delta @ self.weights[i].T) * self.activation_derivative(z_values[i-1])
        
        return weight_gradients, bias_gradients
    
    def compute_loss(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算损失"""
        if self.layer_sizes[-1] == 1:  # 二分类 - 二元交叉熵
            epsilon = 1e-15
            y_pred = np.clip(y_pred, epsilon, 1 - epsilon)
            return -np.mean(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred))
        else:  # 多分类 - 分类交叉熵
            epsilon = 1e-15
            y_pred = np.clip(y_pred, epsilon, 1 - epsilon)
            return -np.mean(np.sum(y_true * np.log(y_pred), axis=1))
    
    def fit(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        self.loss_history = []
        
        for iteration in range(self.max_iterations):
            # 前向传播
            activations, z_values = self.forward_propagation(X)
            
            # 计算损失
            loss = self.compute_loss(y, activations[-1])
            self.loss_history.append(loss)
            
            # 反向传播
            weight_grads, bias_grads = self.backward_propagation(X, y, activations, z_values)
            
            # 更新参数
            for i in range(len(self.weights)):
                self.weights[i] -= self.learning_rate * weight_grads[i]
                self.biases[i] -= self.learning_rate * bias_grads[i]
            
            # 每100次迭代打印一次
            if iteration % 100 == 0:
                print(f"迭代 {iteration}, 损失: {loss:.6f}")
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        activations, _ = self.forward_propagation(X)
        predictions = activations[-1]
        
        if self.layer_sizes[-1] == 1:  # 二分类
            return (predictions >= 0.5).astype(int).flatten()
        else:  # 多分类
            return np.argmax(predictions, axis=1)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """预测概率"""
        activations, _ = self.forward_propagation(X)
        return activations[-1]
    
    def score(self, X: np.ndarray, y: np.ndarray) -> float:
        """计算准确率"""
        predictions = self.predict(X)
        if y.ndim > 1:  # one-hot编码
            y_true = np.argmax(y, axis=1)
        else:
            y_true = y
        return np.mean(predictions == y_true)

def generate_linearly_separable_data(n_samples: int = 100) -> Tuple[np.ndarray, np.ndarray]:
    """生成线性可分数据"""
    np.random.seed(42)
    
    # 类别0
    X0 = np.random.multivariate_normal([0, 0], [[1, 0], [0, 1]], n_samples//2)
    y0 = np.zeros(n_samples//2)
    
    # 类别1
    X1 = np.random.multivariate_normal([3, 3], [[1, 0], [0, 1]], n_samples//2)
    y1 = np.ones(n_samples//2)
    
    X = np.vstack([X0, X1])
    y = np.hstack([y0, y1])
    
    # 打乱数据
    indices = np.random.permutation(n_samples)
    return X[indices], y[indices]

def generate_xor_data(n_samples: int = 200) -> Tuple[np.ndarray, np.ndarray]:
    """生成XOR数据（线性不可分）"""
    np.random.seed(42)
    
    # XOR的四个区域
    X = np.random.uniform(-2, 2, (n_samples, 2))
    y = ((X[:, 0] > 0) ^ (X[:, 1] > 0)).astype(int)  # XOR逻辑
    
    return X, y

def generate_multiclass_data(n_samples: int = 300, n_classes: int = 3) -> Tuple[np.ndarray, np.ndarray]:
    """生成多分类数据"""
    np.random.seed(42)
    
    X = []
    y = []
    
    for class_id in range(n_classes):
        # 每个类别围绕不同的中心
        center = [3 * np.cos(2 * np.pi * class_id / n_classes), 
                 3 * np.sin(2 * np.pi * class_id / n_classes)]
        
        class_X = np.random.multivariate_normal(center, [[0.5, 0], [0, 0.5]], n_samples//n_classes)
        class_y = np.full(n_samples//n_classes, class_id)
        
        X.append(class_X)
        y.append(class_y)
    
    X = np.vstack(X)
    y = np.hstack(y)
    
    # 打乱数据
    indices = np.random.permutation(len(X))
    return X[indices], y[indices]

def to_categorical(y: np.ndarray, num_classes: int = None) -> np.ndarray:
    """转换为one-hot编码"""
    if num_classes is None:
        num_classes = len(np.unique(y))
    
    categorical = np.zeros((len(y), num_classes))
    categorical[np.arange(len(y)), y] = 1
    return categorical

def demonstrate_perceptron():
    """演示感知机"""
    print("=== 感知机演示 ===")
    
    # 生成线性可分数据
    X, y = generate_linearly_separable_data(100)
    
    print(f"数据形状: X={X.shape}, y={y.shape}")
    print(f"类别分布: {np.bincount(y)}")
    
    # 训练感知机
    perceptron = Perceptron(learning_rate=0.1, max_iterations=100)
    perceptron.fit(X, y)
    
    # 评估
    accuracy = perceptron.score(X, y)
    print(f"最终准确率: {accuracy:.4f}")
    print(f"权重: {perceptron.weights}")
    print(f"偏置: {perceptron.bias:.4f}")
    
    # 测试XOR问题（线性不可分）
    print(f"\n测试XOR问题（线性不可分）:")
    X_xor, y_xor = generate_xor_data(100)
    
    perceptron_xor = Perceptron(learning_rate=0.1, max_iterations=100)
    perceptron_xor.fit(X_xor, y_xor)
    
    accuracy_xor = perceptron_xor.score(X_xor, y_xor)
    print(f"XOR问题准确率: {accuracy_xor:.4f}")
    print("⚠️ 单层感知机无法解决XOR问题（线性不可分）")

def demonstrate_activation_functions():
    """演示激活函数"""
    print("\n=== 激活函数演示 ===")
    
    x = np.linspace(-5, 5, 100)
    
    # 计算各种激活函数的值
    sigmoid_vals = ActivationFunctions.sigmoid(x)
    tanh_vals = ActivationFunctions.tanh(x)
    relu_vals = ActivationFunctions.relu(x)
    leaky_relu_vals = ActivationFunctions.leaky_relu(x)
    
    print("激活函数特性:")
    print("1. Sigmoid: 输出范围(0,1)，存在梯度消失问题")
    print("2. Tanh: 输出范围(-1,1)，零中心化")
    print("3. ReLU: 输出范围[0,+∞)，解决梯度消失，但有死神经元问题")
    print("4. Leaky ReLU: 解决ReLU的死神经元问题")
    
    # 测试点
    test_points = [-2, -1, 0, 1, 2]
    print(f"\n测试点: {test_points}")
    
    for point in test_points:
        sigmoid_val = ActivationFunctions.sigmoid(np.array([point]))[0]
        tanh_val = ActivationFunctions.tanh(np.array([point]))[0]
        relu_val = ActivationFunctions.relu(np.array([point]))[0]
        
        print(f"x={point}: Sigmoid={sigmoid_val:.4f}, Tanh={tanh_val:.4f}, ReLU={relu_val:.4f}")

def demonstrate_mlp_binary_classification():
    """演示多层感知机二分类"""
    print("\n=== 多层感知机二分类演示 ===")
    
    # 使用XOR数据测试
    X, y = generate_xor_data(400)
    
    print(f"XOR数据形状: X={X.shape}, y={y.shape}")
    
    # 不同激活函数的比较
    activations = ['sigmoid', 'tanh', 'relu']
    
    for activation in activations:
        print(f"\n使用 {activation} 激活函数:")
        
        mlp = MultiLayerPerceptron(
            layer_sizes=[2, 8, 4, 1],  # 2输入 -> 8隐藏 -> 4隐藏 -> 1输出
            activation=activation,
            learning_rate=0.1,
            max_iterations=500
        )
        
        mlp.fit(X, y)
        
        accuracy = mlp.score(X, y)
        print(f"最终准确率: {accuracy:.4f}")
        
        if accuracy > 0.9:
            print("✓ 成功解决XOR问题！")
        else:
            print("⚠️ 未能很好地解决XOR问题")

def demonstrate_mlp_multiclass():
    """演示多层感知机多分类"""
    print("\n=== 多层感知机多分类演示 ===")
    
    # 生成多分类数据
    X, y = generate_multiclass_data(300, n_classes=4)
    y_categorical = to_categorical(y, num_classes=4)
    
    print(f"多分类数据形状: X={X.shape}, y={y.shape}")
    print(f"类别分布: {np.bincount(y)}")
    
    # 创建多分类网络
    mlp = MultiLayerPerceptron(
        layer_sizes=[2, 16, 8, 4],  # 2输入 -> 16隐藏 -> 8隐藏 -> 4输出
        activation='relu',
        learning_rate=0.01,
        max_iterations=1000
    )
    
    mlp.fit(X, y_categorical)
    
    accuracy = mlp.score(X, y_categorical)
    print(f"最终准确率: {accuracy:.4f}")
    
    # 预测示例
    test_samples = X[:5]
    predictions = mlp.predict(test_samples)
    probabilities = mlp.predict_proba(test_samples)
    
    print(f"\n预测示例:")
    for i in range(5):
        true_label = y[i]
        pred_label = predictions[i]
        pred_probs = probabilities[i]
        
        print(f"样本 {i+1}: 真实={true_label}, 预测={pred_label}, 概率={pred_probs}")

def compare_network_architectures():
    """比较不同网络架构"""
    print("\n=== 网络架构比较 ===")
    
    X, y = generate_xor_data(400)
    
    architectures = [
        [2, 4, 1],      # 浅层网络
        [2, 8, 4, 1],   # 中等深度
        [2, 16, 8, 4, 1] # 较深网络
    ]
    
    for arch in architectures:
        print(f"\n网络架构: {' -> '.join(map(str, arch))}")
        
        mlp = MultiLayerPerceptron(
            layer_sizes=arch,
            activation='relu',
            learning_rate=0.1,
            max_iterations=500
        )
        
        mlp.fit(X, y)
        
        accuracy = mlp.score(X, y)
        final_loss = mlp.loss_history[-1]
        
        print(f"最终准确率: {accuracy:.4f}")
        print(f"最终损失: {final_loss:.6f}")
        
        # 计算参数数量
        total_params = sum(w.size for w in mlp.weights) + sum(b.size for b in mlp.biases)
        print(f"参数数量: {total_params}")

# 主程序演示
if __name__ == "__main__":
    print("神经网络基础学习")
    print("=" * 50)
    
    demonstrate_perceptron()
    demonstrate_activation_functions()
    demonstrate_mlp_binary_classification()
    demonstrate_mlp_multiclass()
    compare_network_architectures()
    
    print("\n" + "=" * 50)
    print("神经网络基础概念总结:")
    print("✓ 感知机：线性分类器，只能解决线性可分问题")
    print("✓ 多层感知机：通过隐藏层解决非线性问题")
    print("✓ 激活函数：引入非线性，使网络能拟合复杂函数")
    print("✓ 前向传播：信息从输入层传递到输出层")
    print("✓ 反向传播：误差从输出层传递到输入层，更新权重")
    print("✓ 万能逼近定理：足够宽的单隐藏层网络可以逼近任意连续函数")
    print("✓ 网络深度：更深的网络可以用更少的神经元表示复杂函数")
    print("\n现在你理解了神经网络的基本原理！")
