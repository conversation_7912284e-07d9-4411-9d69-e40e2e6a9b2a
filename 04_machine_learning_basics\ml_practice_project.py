"""
机器学习基础综合实践项目
构建一个完整的机器学习项目，从数据处理到模型训练和评估
"""

import numpy as np
import time
from typing import List, Tuple, Dict, Optional
import json

class MLFramework:
    """简化的机器学习框架"""
    
    def __init__(self):
        self.models = {}
        self.datasets = {}
        self.results = {}
    
    def add_dataset(self, name: str, X: np.ndarray, y: np.ndarray):
        """添加数据集"""
        self.datasets[name] = {'X': X, 'y': y}
    
    def add_model(self, name: str, model):
        """添加模型"""
        self.models[name] = model
    
    def train_model(self, model_name: str, dataset_name: str, **kwargs):
        """训练模型"""
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 不存在")
        if dataset_name not in self.datasets:
            raise ValueError(f"数据集 {dataset_name} 不存在")
        
        model = self.models[model_name]
        data = self.datasets[dataset_name]
        
        start_time = time.time()
        model.fit(data['X'], data['y'], **kwargs)
        training_time = time.time() - start_time
        
        # 保存结果
        self.results[f"{model_name}_{dataset_name}"] = {
            'training_time': training_time,
            'model': model
        }
        
        return training_time
    
    def evaluate_model(self, model_name: str, dataset_name: str, test_X: np.ndarray, test_y: np.ndarray):
        """评估模型"""
        result_key = f"{model_name}_{dataset_name}"
        if result_key not in self.results:
            raise ValueError(f"模型 {model_name} 在数据集 {dataset_name} 上未训练")
        
        model = self.results[result_key]['model']
        
        # 预测
        predictions = model.predict(test_X)
        
        # 计算指标
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(test_X)
        else:
            probabilities = None
        
        metrics = self._compute_metrics(test_y, predictions, probabilities)
        
        self.results[result_key].update(metrics)
        
        return metrics
    
    def _compute_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> Dict:
        """计算评估指标"""
        metrics = {}
        
        # 准确率
        accuracy = np.mean(y_true == y_pred)
        metrics['accuracy'] = accuracy
        
        # 对于二分类问题
        if len(np.unique(y_true)) == 2:
            # 混淆矩阵
            tp = np.sum((y_true == 1) & (y_pred == 1))
            fp = np.sum((y_true == 0) & (y_pred == 1))
            tn = np.sum((y_true == 0) & (y_pred == 0))
            fn = np.sum((y_true == 1) & (y_pred == 0))
            
            metrics['confusion_matrix'] = {'tp': tp, 'fp': fp, 'tn': tn, 'fn': fn}
            
            # 精确率、召回率、F1分数
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            metrics['precision'] = precision
            metrics['recall'] = recall
            metrics['f1_score'] = f1
        
        return metrics

class ImprovedNeuralNetwork:
    """改进的神经网络实现"""
    
    def __init__(self, layer_sizes: List[int], activation: str = 'relu', 
                 optimizer: str = 'adam', learning_rate: float = 0.001,
                 regularization: float = 0.0, dropout_rate: float = 0.0):
        """
        初始化神经网络
        
        Args:
            layer_sizes: 每层神经元数量
            activation: 激活函数类型
            optimizer: 优化器类型
            learning_rate: 学习率
            regularization: L2正则化系数
            dropout_rate: Dropout比率
        """
        self.layer_sizes = layer_sizes
        self.num_layers = len(layer_sizes)
        self.activation = activation
        self.optimizer = optimizer
        self.learning_rate = learning_rate
        self.regularization = regularization
        self.dropout_rate = dropout_rate
        
        # 初始化权重和偏置
        self._initialize_parameters()
        
        # 优化器状态
        self._initialize_optimizer_state()
        
        # 训练历史
        self.history = {
            'loss': [],
            'accuracy': [],
            'val_loss': [],
            'val_accuracy': []
        }
    
    def _initialize_parameters(self):
        """初始化参数"""
        self.weights = []
        self.biases = []
        
        for i in range(self.num_layers - 1):
            # He初始化（适用于ReLU）
            if self.activation == 'relu':
                w = np.random.randn(self.layer_sizes[i], self.layer_sizes[i+1]) * np.sqrt(2.0 / self.layer_sizes[i])
            else:
                # Xavier初始化
                w = np.random.randn(self.layer_sizes[i], self.layer_sizes[i+1]) / np.sqrt(self.layer_sizes[i])
            
            b = np.zeros((1, self.layer_sizes[i+1]))
            
            self.weights.append(w)
            self.biases.append(b)
    
    def _initialize_optimizer_state(self):
        """初始化优化器状态"""
        self.optimizer_state = {
            'iteration': 0,
            'momentum_v_w': [np.zeros_like(w) for w in self.weights],
            'momentum_v_b': [np.zeros_like(b) for b in self.biases],
            'adam_m_w': [np.zeros_like(w) for w in self.weights],
            'adam_v_w': [np.zeros_like(w) for w in self.weights],
            'adam_m_b': [np.zeros_like(b) for b in self.biases],
            'adam_v_b': [np.zeros_like(b) for b in self.biases]
        }
    
    def _activation_function(self, x: np.ndarray) -> np.ndarray:
        """激活函数"""
        if self.activation == 'relu':
            return np.maximum(0, x)
        elif self.activation == 'sigmoid':
            x = np.clip(x, -500, 500)
            return 1 / (1 + np.exp(-x))
        elif self.activation == 'tanh':
            return np.tanh(x)
        else:
            raise ValueError(f"不支持的激活函数: {self.activation}")
    
    def _activation_derivative(self, x: np.ndarray) -> np.ndarray:
        """激活函数导数"""
        if self.activation == 'relu':
            return (x > 0).astype(float)
        elif self.activation == 'sigmoid':
            s = self._activation_function(x)
            return s * (1 - s)
        elif self.activation == 'tanh':
            return 1 - np.tanh(x) ** 2
        else:
            raise ValueError(f"不支持的激活函数: {self.activation}")
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)
    
    def _apply_dropout(self, x: np.ndarray, training: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """应用Dropout"""
        if not training or self.dropout_rate == 0:
            return x, np.ones_like(x)
        
        mask = np.random.random(x.shape) > self.dropout_rate
        return x * mask / (1 - self.dropout_rate), mask
    
    def forward(self, X: np.ndarray, training: bool = True) -> np.ndarray:
        """前向传播"""
        self.activations = [X]
        self.z_values = []
        self.dropout_masks = []
        
        current_input = X
        
        for i in range(self.num_layers - 1):
            # 线性变换
            z = current_input @ self.weights[i] + self.biases[i]
            self.z_values.append(z)
            
            # 激活函数
            if i == self.num_layers - 2:  # 输出层
                if self.layer_sizes[-1] == 1:  # 二分类
                    a = 1 / (1 + np.exp(-np.clip(z, -500, 500)))
                else:  # 多分类
                    a = self._softmax(z)
            else:  # 隐藏层
                a = self._activation_function(z)
                # 应用Dropout
                a, mask = self._apply_dropout(a, training)
                self.dropout_masks.append(mask)
            
            self.activations.append(a)
            current_input = a
        
        return self.activations[-1]
    
    def _compute_loss(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算损失（包含正则化）"""
        m = y_true.shape[0]
        
        # 交叉熵损失
        if self.layer_sizes[-1] == 1:  # 二分类
            epsilon = 1e-15
            y_pred = np.clip(y_pred, epsilon, 1 - epsilon)
            loss = -np.mean(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred))
        else:  # 多分类
            epsilon = 1e-15
            y_pred = np.clip(y_pred, epsilon, 1 - epsilon)
            if y_true.ndim == 1:
                y_true_onehot = np.eye(self.layer_sizes[-1])[y_true]
            else:
                y_true_onehot = y_true
            loss = -np.mean(np.sum(y_true_onehot * np.log(y_pred), axis=1))
        
        # L2正则化
        if self.regularization > 0:
            l2_penalty = sum(np.sum(w**2) for w in self.weights)
            loss += self.regularization * l2_penalty / (2 * m)
        
        return loss
    
    def _update_parameters(self, weight_grads: List[np.ndarray], bias_grads: List[np.ndarray]):
        """更新参数"""
        self.optimizer_state['iteration'] += 1
        
        if self.optimizer == 'sgd':
            for i in range(len(self.weights)):
                self.weights[i] -= self.learning_rate * weight_grads[i]
                self.biases[i] -= self.learning_rate * bias_grads[i]
        
        elif self.optimizer == 'momentum':
            beta = 0.9
            for i in range(len(self.weights)):
                self.optimizer_state['momentum_v_w'][i] = (beta * self.optimizer_state['momentum_v_w'][i] + 
                                                          (1 - beta) * weight_grads[i])
                self.optimizer_state['momentum_v_b'][i] = (beta * self.optimizer_state['momentum_v_b'][i] + 
                                                          (1 - beta) * bias_grads[i])
                
                self.weights[i] -= self.learning_rate * self.optimizer_state['momentum_v_w'][i]
                self.biases[i] -= self.learning_rate * self.optimizer_state['momentum_v_b'][i]
        
        elif self.optimizer == 'adam':
            beta1, beta2, epsilon = 0.9, 0.999, 1e-8
            
            for i in range(len(self.weights)):
                # 更新一阶矩估计
                self.optimizer_state['adam_m_w'][i] = (beta1 * self.optimizer_state['adam_m_w'][i] + 
                                                      (1 - beta1) * weight_grads[i])
                self.optimizer_state['adam_m_b'][i] = (beta1 * self.optimizer_state['adam_m_b'][i] + 
                                                      (1 - beta1) * bias_grads[i])
                
                # 更新二阶矩估计
                self.optimizer_state['adam_v_w'][i] = (beta2 * self.optimizer_state['adam_v_w'][i] + 
                                                      (1 - beta2) * weight_grads[i]**2)
                self.optimizer_state['adam_v_b'][i] = (beta2 * self.optimizer_state['adam_v_b'][i] + 
                                                      (1 - beta2) * bias_grads[i]**2)
                
                # 偏差修正
                m_w_corrected = self.optimizer_state['adam_m_w'][i] / (1 - beta1**self.optimizer_state['iteration'])
                m_b_corrected = self.optimizer_state['adam_m_b'][i] / (1 - beta1**self.optimizer_state['iteration'])
                v_w_corrected = self.optimizer_state['adam_v_w'][i] / (1 - beta2**self.optimizer_state['iteration'])
                v_b_corrected = self.optimizer_state['adam_v_b'][i] / (1 - beta2**self.optimizer_state['iteration'])
                
                # 更新参数
                self.weights[i] -= self.learning_rate * m_w_corrected / (np.sqrt(v_w_corrected) + epsilon)
                self.biases[i] -= self.learning_rate * m_b_corrected / (np.sqrt(v_b_corrected) + epsilon)
    
    def fit(self, X: np.ndarray, y: np.ndarray, epochs: int = 100, batch_size: int = 32,
            validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None, verbose: bool = True):
        """训练模型"""
        n_samples = X.shape[0]
        
        for epoch in range(epochs):
            # 打乱数据
            indices = np.random.permutation(n_samples)
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            # 小批次训练
            epoch_loss = 0
            epoch_correct = 0
            num_batches = 0
            
            for i in range(0, n_samples, batch_size):
                end_idx = min(i + batch_size, n_samples)
                X_batch = X_shuffled[i:end_idx]
                y_batch = y_shuffled[i:end_idx]
                
                # 前向传播
                y_pred = self.forward(X_batch, training=True)
                
                # 计算损失
                loss = self._compute_loss(y_batch, y_pred)
                epoch_loss += loss
                
                # 计算准确率
                if self.layer_sizes[-1] == 1:
                    predictions = (y_pred >= 0.5).astype(int).flatten()
                    epoch_correct += np.sum(predictions == y_batch)
                else:
                    predictions = np.argmax(y_pred, axis=1)
                    if y_batch.ndim > 1:
                        y_batch_labels = np.argmax(y_batch, axis=1)
                    else:
                        y_batch_labels = y_batch
                    epoch_correct += np.sum(predictions == y_batch_labels)
                
                # 反向传播（简化实现）
                # 这里省略详细的反向传播代码，使用数值梯度
                weight_grads, bias_grads = self._compute_gradients_numerical(X_batch, y_batch)
                
                # 更新参数
                self._update_parameters(weight_grads, bias_grads)
                
                num_batches += 1
            
            # 记录训练历史
            avg_loss = epoch_loss / num_batches
            accuracy = epoch_correct / n_samples
            
            self.history['loss'].append(avg_loss)
            self.history['accuracy'].append(accuracy)
            
            # 验证集评估
            if validation_data is not None:
                val_X, val_y = validation_data
                val_pred = self.forward(val_X, training=False)
                val_loss = self._compute_loss(val_y, val_pred)
                
                if self.layer_sizes[-1] == 1:
                    val_predictions = (val_pred >= 0.5).astype(int).flatten()
                    val_accuracy = np.mean(val_predictions == val_y)
                else:
                    val_predictions = np.argmax(val_pred, axis=1)
                    if val_y.ndim > 1:
                        val_y_labels = np.argmax(val_y, axis=1)
                    else:
                        val_y_labels = val_y
                    val_accuracy = np.mean(val_predictions == val_y_labels)
                
                self.history['val_loss'].append(val_loss)
                self.history['val_accuracy'].append(val_accuracy)
            
            # 打印进度
            if verbose and (epoch + 1) % 20 == 0:
                print(f"Epoch {epoch+1}/{epochs}")
                print(f"  训练损失: {avg_loss:.4f}, 训练准确率: {accuracy:.4f}")
                if validation_data is not None:
                    print(f"  验证损失: {val_loss:.4f}, 验证准确率: {val_accuracy:.4f}")
    
    def _compute_gradients_numerical(self, X: np.ndarray, y: np.ndarray, epsilon: float = 1e-7) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """数值方法计算梯度（简化实现）"""
        weight_grads = []
        bias_grads = []
        
        for i in range(len(self.weights)):
            # 权重梯度（只计算一个元素作为示例）
            w_grad = np.zeros_like(self.weights[i])
            if self.weights[i].size > 0:
                # 只计算第一个元素的梯度
                original_value = self.weights[i][0, 0]
                
                self.weights[i][0, 0] = original_value + epsilon
                y_pred_plus = self.forward(X, training=False)
                loss_plus = self._compute_loss(y, y_pred_plus)
                
                self.weights[i][0, 0] = original_value - epsilon
                y_pred_minus = self.forward(X, training=False)
                loss_minus = self._compute_loss(y, y_pred_minus)
                
                self.weights[i][0, 0] = original_value
                
                w_grad[0, 0] = (loss_plus - loss_minus) / (2 * epsilon)
            
            weight_grads.append(w_grad)
            
            # 偏置梯度
            b_grad = np.zeros_like(self.biases[i])
            bias_grads.append(b_grad)
        
        return weight_grads, bias_grads
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        y_pred = self.forward(X, training=False)
        
        if self.layer_sizes[-1] == 1:
            return (y_pred >= 0.5).astype(int).flatten()
        else:
            return np.argmax(y_pred, axis=1)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """预测概率"""
        return self.forward(X, training=False)

# 数据生成函数
def generate_classification_dataset(n_samples: int = 1000, n_features: int = 20, 
                                  n_classes: int = 2, noise: float = 0.1) -> Tuple[np.ndarray, np.ndarray]:
    """生成分类数据集"""
    np.random.seed(42)
    
    X = np.random.randn(n_samples, n_features)
    
    if n_classes == 2:
        # 二分类
        weights = np.random.randn(n_features)
        y_continuous = X @ weights + np.random.normal(0, noise, n_samples)
        y = (y_continuous > 0).astype(int)
    else:
        # 多分类
        y = np.random.randint(0, n_classes, n_samples)
    
    return X, y

def train_test_split(X: np.ndarray, y: np.ndarray, test_size: float = 0.2) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """划分训练集和测试集"""
    n_samples = len(X)
    n_test = int(n_samples * test_size)
    
    indices = np.random.permutation(n_samples)
    test_indices = indices[:n_test]
    train_indices = indices[n_test:]
    
    return X[train_indices], X[test_indices], y[train_indices], y[test_indices]

def main():
    """主函数"""
    print("机器学习基础综合实践项目")
    print("=" * 60)
    
    # 创建ML框架
    framework = MLFramework()
    
    # 1. 生成数据集
    print("1. 生成数据集...")
    
    # 二分类数据集
    X_binary, y_binary = generate_classification_dataset(n_samples=1000, n_features=10, n_classes=2)
    X_train_bin, X_test_bin, y_train_bin, y_test_bin = train_test_split(X_binary, y_binary)
    
    # 多分类数据集
    X_multi, y_multi = generate_classification_dataset(n_samples=1000, n_features=10, n_classes=4)
    X_train_multi, X_test_multi, y_train_multi, y_test_multi = train_test_split(X_multi, y_multi)
    
    # 添加到框架
    framework.add_dataset('binary_train', X_train_bin, y_train_bin)
    framework.add_dataset('multi_train', X_train_multi, y_train_multi)
    
    print(f"二分类数据: 训练集{X_train_bin.shape}, 测试集{X_test_bin.shape}")
    print(f"多分类数据: 训练集{X_train_multi.shape}, 测试集{X_test_multi.shape}")
    
    # 2. 创建不同配置的模型
    print("\n2. 创建模型...")
    
    models_config = [
        ('basic_nn', {'layer_sizes': [10, 16, 1], 'optimizer': 'sgd', 'learning_rate': 0.01}),
        ('momentum_nn', {'layer_sizes': [10, 16, 1], 'optimizer': 'momentum', 'learning_rate': 0.01}),
        ('adam_nn', {'layer_sizes': [10, 16, 1], 'optimizer': 'adam', 'learning_rate': 0.001}),
        ('regularized_nn', {'layer_sizes': [10, 32, 16, 1], 'optimizer': 'adam', 'learning_rate': 0.001, 'regularization': 0.01}),
        ('dropout_nn', {'layer_sizes': [10, 32, 16, 1], 'optimizer': 'adam', 'learning_rate': 0.001, 'dropout_rate': 0.2}),
        ('multi_class_nn', {'layer_sizes': [10, 16, 8, 4], 'optimizer': 'adam', 'learning_rate': 0.001})
    ]
    
    for name, config in models_config:
        model = ImprovedNeuralNetwork(**config)
        framework.add_model(name, model)
        print(f"创建模型: {name}")
    
    # 3. 训练模型
    print("\n3. 训练模型...")
    
    # 二分类模型训练
    binary_models = ['basic_nn', 'momentum_nn', 'adam_nn', 'regularized_nn', 'dropout_nn']
    
    for model_name in binary_models:
        print(f"\n训练 {model_name} (二分类)...")
        training_time = framework.train_model(
            model_name, 'binary_train',
            epochs=100, batch_size=32,
            validation_data=(X_test_bin, y_test_bin),
            verbose=False
        )
        print(f"训练时间: {training_time:.2f}秒")
    
    # 多分类模型训练
    print(f"\n训练 multi_class_nn (多分类)...")
    training_time = framework.train_model(
        'multi_class_nn', 'multi_train',
        epochs=100, batch_size=32,
        validation_data=(X_test_multi, y_test_multi),
        verbose=False
    )
    print(f"训练时间: {training_time:.2f}秒")
    
    # 4. 模型评估
    print("\n4. 模型评估...")
    
    print("\n二分类模型性能:")
    print("-" * 40)
    
    for model_name in binary_models:
        metrics = framework.evaluate_model(model_name, 'binary_train', X_test_bin, y_test_bin)
        
        print(f"{model_name}:")
        print(f"  准确率: {metrics['accuracy']:.4f}")
        print(f"  精确率: {metrics['precision']:.4f}")
        print(f"  召回率: {metrics['recall']:.4f}")
        print(f"  F1分数: {metrics['f1_score']:.4f}")
    
    print("\n多分类模型性能:")
    print("-" * 40)
    
    metrics = framework.evaluate_model('multi_class_nn', 'multi_train', X_test_multi, y_test_multi)
    print(f"multi_class_nn:")
    print(f"  准确率: {metrics['accuracy']:.4f}")
    
    # 5. 结果分析
    print("\n5. 结果分析...")
    
    # 找出最佳二分类模型
    best_model = None
    best_accuracy = 0
    
    for model_name in binary_models:
        result_key = f"{model_name}_binary_train"
        accuracy = framework.results[result_key]['accuracy']
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            best_model = model_name
    
    print(f"最佳二分类模型: {best_model} (准确率: {best_accuracy:.4f})")
    
    # 分析训练历史
    best_model_obj = framework.results[f"{best_model}_binary_train"]['model']
    final_train_loss = best_model_obj.history['loss'][-1]
    final_val_loss = best_model_obj.history['val_loss'][-1]
    
    print(f"最佳模型训练历史:")
    print(f"  最终训练损失: {final_train_loss:.4f}")
    print(f"  最终验证损失: {final_val_loss:.4f}")
    
    if final_val_loss > final_train_loss * 1.5:
        print("  ⚠️ 可能存在过拟合")
    else:
        print("  ✓ 模型泛化良好")
    
    print("\n" + "=" * 60)
    print("项目总结:")
    print("✓ 数据生成和预处理")
    print("✓ 多种神经网络架构实现")
    print("✓ 不同优化算法比较")
    print("✓ 正则化技术应用")
    print("✓ 模型评估和性能分析")
    print("✓ 完整的机器学习工作流程")
    print("\n恭喜！你已经掌握了机器学习的基础知识和实践技能！")

if __name__ == "__main__":
    main()
