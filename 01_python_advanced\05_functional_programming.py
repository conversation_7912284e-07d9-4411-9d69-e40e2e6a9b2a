"""
函数式编程 - 高阶函数、闭包、lambda
在机器学习中用于数据处理、函数组合、优化器实现等
"""

import functools
from typing import Callable, List, Any, Tuple
import math

# 1. 高阶函数基础
def apply_activation(activation_fn: Callable[[float], float]):
    """返回应用激活函数的函数"""
    def activate_layer(inputs: List[float]) -> List[float]:
        return [activation_fn(x) for x in inputs]
    return activate_layer

# 激活函数
def sigmoid(x: float) -> float:
    """Sigmoid激活函数"""
    return 1 / (1 + math.exp(-max(-500, min(500, x))))  # 防止溢出

def relu(x: float) -> float:
    """ReLU激活函数"""
    return max(0, x)

def tanh(x: float) -> float:
    """Tanh激活函数"""
    return math.tanh(x)

# 2. 闭包和函数工厂
def create_optimizer(learning_rate: float, momentum: float = 0.0):
    """创建优化器函数 - 演示闭包"""
    velocity = {}
    
    def optimize(parameters: dict, gradients: dict) -> dict:
        """优化参数"""
        updated_params = {}
        
        for name, param in parameters.items():
            if name not in velocity:
                velocity[name] = 0
            
            # 动量更新
            velocity[name] = momentum * velocity[name] + learning_rate * gradients[name]
            updated_params[name] = param - velocity[name]
        
        return updated_params
    
    # 添加属性到函数
    optimize.learning_rate = learning_rate
    optimize.momentum = momentum
    optimize.reset = lambda: velocity.clear()
    
    return optimize

def create_loss_function(loss_type: str):
    """损失函数工厂"""
    if loss_type == "mse":
        def mse_loss(predictions: List[float], targets: List[float]) -> float:
            return sum((p - t) ** 2 for p, t in zip(predictions, targets)) / len(predictions)
        return mse_loss
    
    elif loss_type == "mae":
        def mae_loss(predictions: List[float], targets: List[float]) -> float:
            return sum(abs(p - t) for p, t in zip(predictions, targets)) / len(predictions)
        return mae_loss
    
    else:
        raise ValueError(f"未知损失函数类型: {loss_type}")

# 3. 函数组合和管道
def compose(*functions):
    """函数组合 - 从右到左"""
    return functools.reduce(lambda f, g: lambda x: f(g(x)), functions, lambda x: x)

def pipe(value, *functions):
    """管道操作 - 从左到右"""
    return functools.reduce(lambda v, f: f(v), functions, value)

# 数据处理函数
def normalize(data: List[float]) -> List[float]:
    """标准化数据"""
    mean = sum(data) / len(data)
    std = (sum((x - mean) ** 2 for x in data) / len(data)) ** 0.5
    return [(x - mean) / std if std > 0 else 0 for x in data]

def clip_values(min_val: float, max_val: float):
    """创建值裁剪函数"""
    def clip(data: List[float]) -> List[float]:
        return [max(min_val, min(max_val, x)) for x in data]
    return clip

def add_noise(noise_level: float):
    """添加噪声函数"""
    import random
    def add_noise_to_data(data: List[float]) -> List[float]:
        return [x + random.uniform(-noise_level, noise_level) for x in data]
    return add_noise_to_data

# 4. 高级函数式技巧
def curry(func):
    """柯里化装饰器"""
    @functools.wraps(func)
    def curried(*args, **kwargs):
        if len(args) + len(kwargs) >= func.__code__.co_argcount:
            return func(*args, **kwargs)
        return lambda *more_args, **more_kwargs: curried(*(args + more_args), **{**kwargs, **more_kwargs})
    return curried

@curry
def weighted_sum(weights: List[float], values: List[float], bias: float = 0.0) -> float:
    """加权求和函数"""
    return sum(w * v for w, v in zip(weights, values)) + bias

def memoize(func):
    """记忆化装饰器"""
    cache = {}
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        key = str(args) + str(sorted(kwargs.items()))
        if key not in cache:
            cache[key] = func(*args, **kwargs)
        return cache[key]
    
    wrapper.cache = cache
    wrapper.cache_clear = cache.clear
    return wrapper

# 5. 函数式数据处理
def batch_process(batch_size: int):
    """批处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(data: List[Any]) -> List[Any]:
            results = []
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                batch_result = func(batch)
                results.extend(batch_result)
            return results
        return wrapper
    return decorator

@batch_process(batch_size=32)
def process_batch(batch: List[float]) -> List[float]:
    """批处理函数示例"""
    return [x * 2 + 1 for x in batch]

# 6. 函数式神经网络层
class FunctionalLayer:
    """函数式神经网络层"""
    
    def __init__(self, weights: List[float], bias: float, activation: Callable[[float], float]):
        self.weights = weights
        self.bias = bias
        self.activation = activation
    
    def __call__(self, inputs: List[float]) -> List[float]:
        # 使用函数式编程风格
        weighted_inputs = map(lambda pair: pair[0] * pair[1], zip(self.weights, inputs))
        linear_output = sum(weighted_inputs) + self.bias
        return self.activation(linear_output)

# 演示使用
if __name__ == "__main__":
    print("=== 函数式编程演示 ===")
    
    # 1. 高阶函数和激活函数
    print("1. 激活函数应用:")
    inputs = [-2, -1, 0, 1, 2]
    
    sigmoid_layer = apply_activation(sigmoid)
    relu_layer = apply_activation(relu)
    
    print(f"输入: {inputs}")
    print(f"Sigmoid: {[f'{x:.3f}' for x in sigmoid_layer(inputs)]}")
    print(f"ReLU: {relu_layer(inputs)}")
    
    # 2. 闭包和优化器
    print("\n2. 优化器闭包:")
    optimizer = create_optimizer(learning_rate=0.01, momentum=0.9)
    
    params = {"weight": 1.0, "bias": 0.5}
    grads = {"weight": 0.1, "bias": 0.05}
    
    print(f"原始参数: {params}")
    updated_params = optimizer(params, grads)
    print(f"更新后参数: {updated_params}")
    
    # 3. 损失函数工厂
    print("\n3. 损失函数:")
    mse_loss = create_loss_function("mse")
    mae_loss = create_loss_function("mae")
    
    predictions = [1.0, 2.0, 3.0]
    targets = [1.1, 1.9, 3.2]
    
    print(f"预测: {predictions}")
    print(f"目标: {targets}")
    print(f"MSE损失: {mse_loss(predictions, targets):.4f}")
    print(f"MAE损失: {mae_loss(predictions, targets):.4f}")
    
    # 4. 函数组合和管道
    print("\n4. 数据处理管道:")
    raw_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    # 使用管道
    processed_data = pipe(
        raw_data,
        normalize,
        clip_values(-1.5, 1.5),
        add_noise(0.1)
    )
    
    print(f"原始数据: {raw_data}")
    print(f"处理后: {[f'{x:.3f}' for x in processed_data]}")
    
    # 使用函数组合
    process_pipeline = compose(
        add_noise(0.05),
        clip_values(-2, 2),
        normalize
    )
    
    composed_result = process_pipeline(raw_data)
    print(f"组合处理: {[f'{x:.3f}' for x in composed_result]}")
    
    # 5. 柯里化
    print("\n5. 柯里化:")
    weights = [0.5, -0.3, 0.8]
    
    # 部分应用
    neuron_with_weights = weighted_sum(weights)
    
    inputs1 = [1.0, 2.0, -1.0]
    inputs2 = [0.5, 1.5, 0.8]
    
    print(f"权重: {weights}")
    print(f"输入1 {inputs1} -> 输出: {neuron_with_weights(inputs1):.3f}")
    print(f"输入2 {inputs2} -> 输出: {neuron_with_weights(inputs2):.3f}")
    
    # 6. 记忆化
    print("\n6. 记忆化:")
    
    @memoize
    def expensive_computation(n):
        """模拟昂贵的计算"""
        print(f"计算 {n}...")
        return sum(i**2 for i in range(n))
    
    print(f"第一次调用: {expensive_computation(1000)}")
    print(f"第二次调用: {expensive_computation(1000)}")  # 从缓存获取
    print(f"缓存大小: {len(expensive_computation.cache)}")
    
    # 7. 批处理
    print("\n7. 批处理:")
    large_data = list(range(100))
    processed = process_batch(large_data)
    print(f"处理了 {len(large_data)} 个数据点")
    print(f"前10个结果: {processed[:10]}")
    
    # 8. 函数式层
    print("\n8. 函数式神经网络层:")
    layer = FunctionalLayer(
        weights=[0.5, -0.3, 0.8],
        bias=0.1,
        activation=sigmoid
    )
    
    test_input = [1.0, 2.0, -1.0]
    output = layer(test_input)
    print(f"层输入: {test_input}")
    print(f"层输出: {output:.4f}")
