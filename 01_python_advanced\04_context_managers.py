"""
上下文管理器 - 资源管理和状态控制
在机器学习中用于模型状态管理、GPU内存管理、训练/评估模式切换等
"""

import time
import contextlib
from typing import Any, Optional

# 1. 基础上下文管理器类
class Timer:
    """计时上下文管理器"""
    
    def __init__(self, name: str = "操作"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        print(f"开始 {self.name}...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        print(f"{self.name} 完成，耗时: {duration:.4f}秒")
        
        # 处理异常
        if exc_type is not None:
            print(f"发生异常: {exc_type.__name__}: {exc_val}")
            return False  # 不抑制异常
        return True
    
    @property
    def duration(self):
        """获取执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None

# 2. 模型状态管理器
class ModelMode:
    """模型模式上下文管理器 - 模拟PyTorch的训练/评估模式"""
    
    def __init__(self, model, training: bool):
        self.model = model
        self.training = training
        self.previous_mode = None
    
    def __enter__(self):
        self.previous_mode = getattr(self.model, 'training', True)
        self.model.training = self.training
        mode_name = "训练" if self.training else "评估"
        print(f"模型切换到{mode_name}模式")
        return self.model
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.model.training = self.previous_mode
        mode_name = "训练" if self.previous_mode else "评估"
        print(f"模型恢复到{mode_name}模式")

# 3. 资源管理器
class GPUMemoryManager:
    """GPU内存管理器（模拟）"""
    
    def __init__(self, device_id: int = 0):
        self.device_id = device_id
        self.allocated_memory = 0
    
    def __enter__(self):
        print(f"初始化GPU {self.device_id}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        print(f"清理GPU {self.device_id} 内存: {self.allocated_memory}MB")
        self.allocated_memory = 0
    
    def allocate(self, size_mb: int):
        """分配内存"""
        self.allocated_memory += size_mb
        print(f"分配 {size_mb}MB GPU内存，总计: {self.allocated_memory}MB")

# 4. 使用contextlib的函数式上下文管理器
@contextlib.contextmanager
def temporary_seed(seed: int):
    """临时设置随机种子"""
    import random
    old_state = random.getstate()
    random.seed(seed)
    try:
        print(f"设置随机种子为 {seed}")
        yield
    finally:
        random.setstate(old_state)
        print("恢复原始随机状态")

@contextlib.contextmanager
def suppress_output():
    """抑制输出"""
    import sys
    import os
    
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    
    try:
        with open(os.devnull, 'w') as devnull:
            sys.stdout = devnull
            sys.stderr = devnull
            yield
    finally:
        sys.stdout = old_stdout
        sys.stderr = old_stderr

@contextlib.contextmanager
def gradient_clipping(model, max_norm: float):
    """梯度裁剪上下文管理器（模拟）"""
    print(f"启用梯度裁剪，最大范数: {max_norm}")
    try:
        yield model
    finally:
        # 模拟梯度裁剪
        print("应用梯度裁剪")

# 5. 嵌套上下文管理器
class TrainingSession:
    """训练会话管理器"""
    
    def __init__(self, model, device_id: int = 0):
        self.model = model
        self.device_id = device_id
        self.timer = Timer("训练会话")
        self.gpu_manager = GPUMemoryManager(device_id)
        self.mode_manager = ModelMode(model, training=True)
    
    def __enter__(self):
        # 按顺序进入所有上下文
        self.timer.__enter__()
        self.gpu_manager.__enter__()
        self.mode_manager.__enter__()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 按相反顺序退出所有上下文
        self.mode_manager.__exit__(exc_type, exc_val, exc_tb)
        self.gpu_manager.__exit__(exc_type, exc_val, exc_tb)
        self.timer.__exit__(exc_type, exc_val, exc_tb)

# 6. 模拟模型类
class SimpleModel:
    """简单模型类"""
    
    def __init__(self):
        self.training = True
        self.parameters = [1, 2, 3, 4, 5]
    
    def forward(self, x):
        """前向传播"""
        if self.training:
            print("训练模式：应用dropout和batch norm")
        else:
            print("评估模式：不应用dropout和batch norm")
        return sum(p * x for p in self.parameters)
    
    def train_step(self):
        """训练步骤"""
        print("执行训练步骤...")
        time.sleep(0.1)  # 模拟计算时间

# 演示使用
if __name__ == "__main__":
    print("=== 上下文管理器演示 ===")
    
    # 1. 基础计时器
    print("1. 计时器:")
    with Timer("矩阵计算") as timer:
        # 模拟一些计算
        result = sum(i**2 for i in range(1000))
        print(f"计算结果: {result}")
    print(f"总耗时: {timer.duration:.4f}秒")
    
    # 2. 模型模式管理
    print("\n2. 模型模式管理:")
    model = SimpleModel()
    print(f"初始模式: {'训练' if model.training else '评估'}")
    
    with ModelMode(model, training=False):
        model.forward(2.0)
    
    print(f"恢复后模式: {'训练' if model.training else '评估'}")
    
    # 3. GPU内存管理
    print("\n3. GPU内存管理:")
    with GPUMemoryManager(device_id=0) as gpu:
        gpu.allocate(512)
        gpu.allocate(256)
    
    # 4. 临时随机种子
    print("\n4. 临时随机种子:")
    import random
    
    print("正常随机数:")
    print([random.randint(1, 10) for _ in range(3)])
    
    with temporary_seed(42):
        print("固定种子随机数:")
        print([random.randint(1, 10) for _ in range(3)])
    
    print("恢复后随机数:")
    print([random.randint(1, 10) for _ in range(3)])
    
    # 5. 梯度裁剪
    print("\n5. 梯度裁剪:")
    with gradient_clipping(model, max_norm=1.0):
        model.train_step()
    
    # 6. 训练会话（嵌套上下文管理器）
    print("\n6. 训练会话:")
    with TrainingSession(model, device_id=0) as session:
        session.gpu_manager.allocate(1024)
        model.train_step()
        model.train_step()
    
    # 7. 多个上下文管理器组合
    print("\n7. 上下文管理器组合:")
    with Timer("组合操作"), \
         temporary_seed(123), \
         ModelMode(model, training=False):
        
        result = model.forward(3.0)
        random_nums = [random.randint(1, 100) for _ in range(5)]
        print(f"模型输出: {result}")
        print(f"随机数: {random_nums}")
