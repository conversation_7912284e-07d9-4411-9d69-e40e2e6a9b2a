# 机器学习基础

## 学习目标
理解机器学习的核心概念和算法，为深度学习和Transformer的学习打下坚实基础。重点掌握梯度下降、反向传播等核心算法的原理和实现。

## 学习内容

### 1. 机器学习基本概念
- **监督学习、无监督学习、强化学习**
  - 分类、回归、聚类问题
  - 训练集、验证集、测试集
  - 过拟合和欠拟合
- **损失函数和优化**
  - 均方误差、交叉熵损失
  - 梯度下降算法
  - 学习率和收敛性

### 2. 线性模型
- **线性回归**
  - 最小二乘法
  - 正规方程解
  - 梯度下降求解
- **逻辑回归**
  - Sigmoid函数
  - 最大似然估计
  - 二分类和多分类

### 3. 神经网络基础
- **感知机**
  - 单层感知机
  - 线性可分性
  - 感知机学习算法
- **多层感知机**
  - 隐藏层的作用
  - 激活函数（Sigmoid、ReLU、Tanh）
  - 万能逼近定理

### 4. 反向传播算法
- **前向传播**
  - 层间信息传递
  - 激活函数的应用
  - 输出计算
- **反向传播**
  - 链式法则的应用
  - 梯度计算
  - 参数更新

### 5. 优化算法
- **梯度下降变种**
  - 批量梯度下降
  - 随机梯度下降
  - 小批量梯度下降
- **高级优化器**
  - Momentum
  - Adam
  - RMSprop

### 6. 正则化技术
- **L1和L2正则化**
  - 权重衰减
  - 特征选择
- **Dropout**
  - 随机失活
  - 防止过拟合
- **早停法**
  - 验证集监控
  - 最佳模型保存

### 7. 模型评估
- **评估指标**
  - 准确率、精确率、召回率
  - F1分数、AUC-ROC
  - 混淆矩阵
- **交叉验证**
  - K折交叉验证
  - 留一法
  - 分层采样

## 实践项目
1. 从零实现线性回归和逻辑回归
2. 构建多层感知机并实现反向传播
3. 比较不同优化算法的效果
4. 实现正则化技术防止过拟合
5. 完整的机器学习项目流程

## 学习时间
预计 6-8 天

## 重点关注
- **梯度下降** - 优化算法的基础
- **反向传播** - 神经网络训练的核心
- **损失函数** - 模型优化的目标
- **正则化** - 提高模型泛化能力

## 与深度学习的联系
- 线性模型 → 神经网络层
- 反向传播 → 深度网络训练
- 优化算法 → 深度学习优化
- 正则化 → 深度学习正则化技术

## 下一步
完成后进入深度学习框架（PyTorch）学习阶段
